/*
<PERSON>okai style - ported by <PERSON> - http://grigio.org
*/

pre code {
  display: block; padding: 0.5em;
  background: #272822;
}

pre .tag,
pre .tag .title,
pre .keyword,
pre .literal,
pre .change,
pre .winutils,
pre .flow,
pre .lisp .title,
pre .clojure .built_in,
pre .nginx .title,
pre .tex .special {
  color: #F92672;
}

pre code {
  color: #DDD;
}

pre code .constant {
	color: #66D9EF;
}

pre .class .title {
	color: white;
}

pre .attribute,
pre .symbol,
pre .symbol .string,
pre .value,
pre .regexp {
	color: #BF79DB;
}

pre .tag .value,
pre .string,
pre .subst,
pre .title,
pre .haskell .type,
pre .preprocessor,
pre .ruby .class .parent,
pre .built_in,
pre .sql .aggregate,
pre .django .template_tag,
pre .django .variable,
pre .smalltalk .class,
pre .javadoc,
pre .django .filter .argument,
pre .smalltalk .localvars,
pre .smalltalk .array,
pre .attr_selector,
pre .pseudo,
pre .addition,
pre .stream,
pre .envvar,
pre .apache .tag,
pre .apache .cbracket,
pre .tex .command,
pre .prompt {
  color: #A6E22E;
}

pre .comment,
pre .java .annotation,
pre .python .decorator,
pre .template_comment,
pre .pi,
pre .doctype,
pre .deletion,
pre .shebang,
pre .apache .sqbracket,
pre .tex .formula {
  color: #75715E;
}

pre .keyword,
pre .literal,
pre .css .id,
pre .phpdoc,
pre .title,
pre .haskell .type,
pre .vbscript .built_in,
pre .sql .aggregate,
pre .rsl .built_in,
pre .smalltalk .class,
pre .diff .header,
pre .chunk,
pre .winutils,
pre .bash .variable,
pre .apache .tag,
pre .tex .special,
pre .request,
pre .status {
  font-weight: bold;
}

pre .coffeescript .javascript,
pre .javascript .xml,
pre .tex .formula,
pre .xml .javascript,
pre .xml .vbscript,
pre .xml .css,
pre .xml .cdata {
  opacity: 0.5;
}
