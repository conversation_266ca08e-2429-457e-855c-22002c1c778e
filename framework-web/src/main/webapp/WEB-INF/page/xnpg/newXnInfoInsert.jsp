<%--
  Created by IntelliJ IDEA.
  User: DELL
  Date: 2022/7/1
  Time: 9:57
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="en">
<head>
    <title>Title</title>
</head>
<body>
    <div class="main-container">
        <div class="padding-md">
            <div class="row">
                <div class="col-lg-12">
                    <div class="smart-widget widget-blue">
                        <div class="smart-widget-header font-16">
                            考核指标填报
                        </div>
                        <div class="smart-widget-inner">
                            <div class="smart-widget-body form-horizontal">
<%--                                <form id="dataForm">--%>
                                    <div class="form-group">
                                        <label for="inputtext3" class="control-label" style="padding:10px;float: left">行政区划：</label>
                                        <div class="col-md-2">
                                            <select class="form-control" id="law_object_province"
                                                    name="law_object_province">
                                                <option value="35000000">福建省</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <select class="form-control" id="areaCode" name="areaCode">
                                            </select>
                                        </div>
                                        <label for="inputtext3" class="control-label"  style="padding:10px;margin-left:100px;float: left">填报年度：</label>
                                        <div class="col-md-2">
                                            <input type="text" placeholder="年度" class="form-control" name="years" id="years">
                                        </div>
                                        <div class="col-md-2" style="margin-left: 100px;">
                                            <button class="btn btn-info" style="width: 100px;"
                                                    onclick="queryData()">查询</button>
                                            <button class="btn btn-info" style="width: 100px;"
                                                    onclick="ResetFun()">重置</button>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="control-label" style="padding:10px;float: left">填报状态：</label>
                                        <div class="col-md-2">
                                            <select class="form-control" name="status" id ="status" onchange="queryData()">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="1" >填报中 </option>
                                                <option value="2">已填报 </option>
                                            </select>
                                        </div>
                                    </div>
<%--                                </form>--%>
                                <div class="form-group">
                                    <div class="col-lg-3">
                                        <c:if test="${isProvince=='1'}">
                                            <button class="btn btn-info" onclick="addFun()">新增填报任务</button>
                                        </c:if>


                                    </div>
                                </div>
                                <div class="form-horizontal">
                                    <table class="table table-striped no-margin table-no-bordered" id="dataTable">

                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        var pageNumber=1 ;
        var pageSize=15;
        $(document).ready(function(){
            initCity();
            //设置查询enter键无效
            // business.listenEnter('selectCount');
            $('#dataTable').bootstrapTable({
                method: 'post',
                dataType: "json",
                url:  WEBPATH+'/score/taskList',
                undefinedText : '-',
                pagination : true, // 分页
                striped : true, // 是否显示行间隔色
                cache : false, // 是否使用缓存
                pageSize: 15, // 设置默认分页为 20
                pageNumber: 1,
                queryParamsType: "",
                pageList: [5, 15, 30, 50, 100, 200], // 自定义分页列表
                singleSelect: false,
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                // pageList : [ 5, 10, 20 ],
                // showColumns : true, // 显示隐藏列
                sidePagination: "server", //服务端请求
                queryParams: queryLawObjectParams,//参数
                uniqueId : "uuid", // 每一行的唯一标识
                columns: [
                    {
                        field:"",
                        title:"序号",
                        align:"center",
                        formatter: function(value,row,index){
                            return index+1;
                        }
                    },
                    {
                        field: "areaName",
                        title: "行政区划",
                        align: 'center'
                    },
                    {
                        field: "years",
                        title: "填报年度",
                        align: 'center'
                    },
                    {
                        field: "uploadDate",
                        title: "填报时间",
                        align: 'center'
                    },
                    {
                        field: "status",
                        title: "填报状态",
                        align: 'center',
                        formatter: function(value, row, index){
                            if(value=='2'){
                                return '已填报';
                            } else if (value=='1'){
                                return '填报中';
                            } else {
                                return '-';
                            }
                        }
                    },
                    {
                        field: "uuid",
                        title: "操作",
                        align: 'center',
                        valign:'middle',
                        width:100,
                        formatter: function(value,row,index){
                            debugger
                            var html="";
                            var isProvince="${isProvince}";
                            // if(row.status=='1' && isProvince =='0') {
                            if(row.status=='1') {
                                html += "<a onclick=\"addXnfoInsert('" + row.uuid + "')\" style=\"cursor:pointer;\"><i class='fa fa-pencil-square-o' style='color:#23b7e5;'>填报</i></a>"
                            }else if(row.status=='2'){
                                // html += "<a onclick=\"detailCase('" + row.uuid + "')\" style=\"cursor:pointer;\"><i class='fa fa-pencil-square-o' style='color:#23b7e5;'>详情</i></a>"
                                html += "<a onclick=\"addXnfoInsert('" + row.uuid + "')\" style=\"cursor:pointer;\"><i class='fa fa-pencil-square-o' style='color:#23b7e5;'>详情</i></a>"
                            }
                            return html;
                        }
                    }
                ],
                responseHandler : function(res) {
                    return {
                        total : res.total,
                        rows : res.list
                    };
                },
                onCheck: function(row, $element) {

                },//单击row事件
                onUncheck: function(row, $element) {

                },
                onUncheckAll: function(row, $element) {

                },
                onCheckAll:function(row, $element) {

                },
                onRefresh: function () {

                },
                formatLoadingMessage: function () {
                    return "请稍等，正在加载中...";
                },
                formatNoMatches: function () { //没有匹配的结果
                    return '无符合条件的记录';
                }
            });



            //绑定搜索按钮
            // $('#selectCount').click(function() {
            //     // $('#dataTable').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:15});
            //     // $('#dataTable').bootstrapTable('refreshOptions',queryLawObjectParams({pageNumber:1,pageSize:15}));
            //     // $("#dataTable").bootstrapTable('destroy');
            //     // $('#dataTable').bootstrapTable('refresh');
            //     $('#dataTable').bootstrapTable('refresh');
            // });
        });
        function queryData(){
            $('#dataTable').bootstrapTable('refresh');
        }
        function addXnfoInsert(id) {
            // business.addMainContentParserHtml(WEBPATH+ '/assessment/toDetail?taskid='+id,null);
            business.addMainContentParserHtml(WEBPATH+ '/jckh/newXnInfoInsert?taskid='+id,null);
        }

        //重置
        function ResetFun(){

        }

        //查看
        function detailCase() {
            // business.addMainContentParserHtml(WEBPATH+ '/jckh/newXnInfoInsert',null);
        }


        function addFun() {
            // business.addMainContentParserHtml(WEBPATH+ '/jckh/newXnInfoInsert',null);
            $.ajax({
                type:"post",
                url:WEBPATH+"/score/createTask",
                dataType:"json",
                async:false,
                data:{},
                success:function(data){
                    $('#dataTable').bootstrapTable('refreshOptions',queryLawObjectParams({pageNumber:1,pageSize:15}));
                }
            });
        }


        function initCity(){
            $.ajax({
                type:"post",
                url:WEBPATH+"/tArea/chickUserArea",
                dataType:"json",
                async:false,
                data:{},
                success:function(data){
                    if(data.cityStatus =='1'){
                        //省级用户
                        $.ajax({
                            type:"post",
                            url:WEBPATH+"/tArea/cityList",
                            dataType:"json",
                            async:false,
                            success:function(data){
                                $("#areaCode").append("<option value=''>请选择</option>");
                                $.each(data,function(i,item){
                                    $("#areaCode").append("<option value="+item.code+">"+item.name+"</option>");
                                });
                            }
                        });
                    }else{
                        //市级用户
                        $("#areaCode").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
                    }
                }
            });
        }

        function queryLawObjectParams(params) {
            var areaCode =$("#areaCode").val();
            var years =$("#years").val();
            var status = $("#status").val();
            var temp = {  //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                pageNum: params.pageNumber,
                pageSize: params.pageSize,
                areaCode:areaCode,
                years:years,
                status:status
            };
            return temp;
        }

</script>
</body>
</html>
