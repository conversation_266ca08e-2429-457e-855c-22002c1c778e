<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>

<!DOCTYPE html>
<html>
<head>

</head>
<body>
	
	<div class="tab-pane fade" id="xcxc">
		<div class="form-horizontal">

			<div class="row padding-md">
				<div class="col-lg-10 col-md-10">
					<div class="timeline-wrapper clearfix" id="objectComment">
						<ul>
							<li v-for="(item,index) in year">
								<div class="timeline-year font-semi-bold">{{item}}年</div>
								<div class="timeline-row alt" v-for="(item1,index1) in month">
									<div class="timeline-item" v-if="contains(item,item1)">
										<div class="timeline-icon">{{item1 | getMonth}}月</div>
										<div class="timeline-item-inner" v-for="(item2,index2) in map[item1]">
											<div>
												<div class="font-14 padding-xs">
													<span>{{item2.creatDate}}</span><span
														style="padding: 0 50px 0 100px;">{{item2.creatUserName}}</span><span>{{item2.creatDepartmentName}}</span>
												</div>
												<div class="font-14 padding-xs">{{item2.title}}</div>
												
												<div  v-if="item2.lawCircleFiles !=null && item2.lawCircleFiles.length>0">
														<img class="padding-xs" v-for="(item3,index3) in item2.lawCircleFiles" :src="item3.fileUrl | getFileUrl" v-on:click="showImgModal(item3.lawCircleId,item3.originallyImgId)"
															style="width: 100px;height:80px; cursor: pointer;">
												</div>
											</div>
											<hr>
										</div>
									</div>
								</div>
							</li>
							<li v-if="year.length==0">
							<div  style="text-align: center;color: red;margin-top: 10px">没有现场巡查数据！</div>
							</li>
						</ul>
					
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- 附件预览modal -->
	<div class="modal fade" id="fileModal" tabindex="-1" 
		role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-md">
			<div class="modal-content" id="modalContent">
				
			</div>
		</div>
	</div>
</body>
<script type="text/javascript">
var imageServer = window.parent.$("#fastdfsServer").val();
var vm = new Vue({
	el:'#objectComment',
	data:{
		year:[],
		month:[],
		map:{
			
		}
		
	},
	methods: {
		contains:function(year,month){
			var temp = month.substring(0,4);
			if(temp==year){
				return true;
			}
			return false;
		},
		showImgModal:function(lawCircleId,orgImageId){
			 $.ajax({
                 type:"post",
                 url:WEBPATH+"/zfdx/showImageModal",
                 dataType:"json",
                 data:{
                	 lawCircleId:lawCircleId,
                	 currentImageOrgId:orgImageId
                 },
                 success:function(data){
                 		$("#fileModal").modal('show');
                 		$("#modalContent").empty();
                 		var html = '';
                 		html = '<div class="modal-header"><div style="float: right; margin-top: -5px;">'
                 		+'<button type="button" class="btn btn-default" onclick="closeModal()">关闭</button>'
                 		+'</div><h4 class="modal-title" id="myModalLabel">预览</h4></div>'
                 		+'<div class="modal-body"><div class="col-sm-12"><div id="myCarousel" class="carousel slide">'
                 		+'<ol class="carousel-indicators">';
                 	
                 		$.each(data,function(i,item){
        					if(item.selected !=null &&item.selected=='1'){
        						html = html + '<li data-target="#myCarousel" data-slide-to="'+i+'" class="active"></li>';
        					}else{
        						html = html + '<li data-target="#myCarousel" data-slide-to="'+i+'" ></li>';
        					}
        				});
                 		html = html + '</ol><div class="carousel-inner">';
                 		$.each(data,function(i,item){
        					if(item.selected !=null &&item.selected=='1'){
        						html = html + '<div class="item active"><img src="'+imageServer+''+item.fileUrl+'" style="width: 100%;height:558px; "'
        						+'alt="'+i+' slide"></div>';
        					}else{
        						html = html + '<div class="item"><img src="'+imageServer+''+item.fileUrl+'" style="width: 100%; height:558px;"'
        						+'alt="'+i+' slide"></div>';
        					}
        				});
                 		html = html + '</div><a class="left carousel-control" href="#myCarousel" role="button"'
         				+'data-slide="prev"><span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>'
         				+'<span class="sr-only">Previous</span></a> <a class="right carousel-control" href="#myCarousel" role="button" data-slide="next">'
         				+'<span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>'+'<span class="sr-only">Next</span></a>'
         				+'</div></div></div><div class="modal-footer"></div>';
                 		$("#modalContent").append(html);
                 		
                 		
                 }
             });
		}
	},
	filters:{
		getMonth:function(value){
			var month = value.substring(4,5);
			if(month=='0'){
				return value.substring(5);
			}else{
				return value.substring(4);
			}
			
		},
		getFileUrl:function(url){
			return  imageServer + url;
		}
	}
});
	function loadObjectCommentTree() {
		var lawObjectId = '${lawObject.id}';
		$.ajax({
					type : "post",
					url : WEBPATH + "/zfdx/getObjectCommentTreeData",
					data : {
						lawObjectId:lawObjectId
					},
					dataType : "json",
					success : function(data) {
						console.log(data);
						vm.$data.year = data.year;
						vm.$data.month = data.month;
						vm.$data.map = data.datas;
					
					}
				});
	}
	
	function closeModal(){
		$("#fileModal").modal('hide');
	}
</script>
</html>