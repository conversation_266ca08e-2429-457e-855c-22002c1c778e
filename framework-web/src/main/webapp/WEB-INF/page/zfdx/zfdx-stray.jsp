<%@ page language="java" import="java.util.*" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta charset="utf-8">
</head>
<body>
<input id="groundBackParams" name="groundBackParams" type="hidden" value='${params }'/><!-- 返回所需的参数 -->
<div class="main-container">
    <div class="padding-md">
        <div class="row">
            <div class="col-md-12">
                <div class="smart-widget">
                    <div class="smart-widget-inner">
                        <div class="smart-widget-body form-horizontal">

                            <form id="searchForm">
                                <input type="hidden" value="5" id="typeCode" name="typeCode">

                                <div class="form-group">
                                    <label class="control-label col-lg-2">名称</label>
                                    <div class="col-lg-3">
                                        <input type="text" placeholder="名称" id="objectName" name="objectName"
                                               class="form-control" data-parsley-required="true">
                                    </div>
                                    <label class="control-label col-lg-2">证照号</label>
                                    <div class="col-lg-3">
                                        <input type="text" placeholder="证照号" id='cardNumber' name="cardNumber"
                                               class="form-control" data-parsley-required="true">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-lg-2">所属流域代码</label>
                                    <div class="col-lg-3">
                                        <input type="text" placeholder="所属流域代码" class="form-control"
                                               data-parsley-required="true" id="wscd" name="WSCD">
                                    </div>
                                    <label class="control-label col-lg-2">所属流域名称</label>
                                    <div class="col-lg-3">
                                        <input type="text" placeholder="所属流域名称" class="form-control"
                                               data-parsley-required="true" id="wsnm" name="WSNM">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-md-2 col-lg-2 col-sm-2">所在行政区</label>
                                    <div class="col-md-2 col-lg-2 col-sm-2">
                                        <select
                                                class="form-control" id='belongProvince' name="belongProvince">
                                            <option value="35000000">福建省</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 col-lg-3 col-sm-3">
                                        <select
                                                class="form-control" id="belongCity" name="belongCity">
                                        </select>
                                    </div>
                                    <div class="col-md-3 col-lg-3 col-sm-3">
                                        <select
                                                class="form-control" id="belongCountry" name="belongCountry">
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-md-2 col-lg-2 col-sm-2">权属行政区</label>
                                    <div class="col-md-2 col-lg-2 col-sm-2">
                                        <select
                                                class="form-control" id="powerProvince" name="powerProvince">
                                            <option value="35000000">福建省</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 col-lg-3 col-sm-3">
                                        <select
                                                class="form-control" id="powerCity" name="powerCity">
                                        </select>
                                    </div>
                                    <div class="col-md-3 col-lg-3 col-sm-3">
                                        <select
                                                class="form-control" id="powerCountry" name="powerCountry">
                                        </select>
                                    </div>
                                </div>
                                <!-- 双随机条件-开始 -->
                                <div class="form-group">
                                    <label class="control-label col-md-2 col-lg-2 col-sm-2">是否纳入双随机</label>
                                    <div class="col-md-3 col-lg-3 col-sm-3">
                                        <select class="form-control" id="isRandom" name="isRandom">
                                            <option value="">全部</option>
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </div>

                                </div>
                                <div class="form-group" id="controlShow_area" style="display: none;">
                                    <label class="control-label col-md-2 col-lg-2 col-sm-2">双随机行政区范围</label>
                                    <div class="col-md-2 col-lg-2 col-sm-2">
                                        <select class="form-control" id='randomProvince' name="randomProvince">
                                            <option value="35000000">福建省</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2 col-lg-2 col-sm-2">
                                        <select
                                                class="form-control" id="randomCity" name="randomCity">
                                        </select>
                                    </div>
                                    <div class="col-md-2 col-lg-2 col-sm-2">
                                        <select
                                                class="form-control" id="randomCountry" name="randomCountry">
                                        </select>
                                    </div>
                                    <div class="col-md-2 col-lg-2 col-sm-2">
                                        <input type="checkbox" id="selectThis" value="" name="selectThis">
                                        <label for="selectThis" class="checkbox-blue" checked>只选本级</label>
                                    </div>
                                </div>
                                <!-- 双随机条件-结束 -->
                                <div class='form-group'>
                                    <div id="controlShow_attr" style="display: none;">
                                        <label class="control-label col-md-2 col-lg-2 col-sm-2">双随机属性</label>
                                        <div class="col-md-3 col-lg-3 col-sm-3">
                                            <div class="input-group">
                                                <input type="hidden" id="randomAttr" name="randomAttr">
                                                <input type="text" id="randomAttrName" name="randomAttrName"
                                                       class="form-control" data-parsley-required="true"
                                                       placeholder="双随机属性" class="form-control" readonly="readonly">
                                                <div class="input-group-btn">
                                                    <button type="button" class="btn btn-info no-shadow" tabindex="-1"
                                                            data-toggle="modal" id="doubleRandomModel"
                                                            data-remote="${webpath}/zfdx/enterprisesDoubleRandomModel"
                                                            data-target="#update">选择
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="form-group">

                                    <label class="control-label col-md-2 col-lg-2 col-sm-2">行业</label>
                                    <div class="col-md-3 col-lg-3 col-sm-3">
                                        <div class="input-group">
                                            <input type="hidden" class="form-control" data-parsley-required="true"
                                                   id='industryTypeCode' name="industryTypeCode">
                                            <input type="text" placeholder="行业" readonly="readonly" class="form-control"
                                                   data-parsley-required="true" id='industryTypeName'
                                                   name="industryTypeName">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-info no-shadow"
                                                        tabindex="-1" data-toggle="modal"
                                                        data-remote="${webpath}/zfdx/enterprises-industry-type-page"
                                                        data-target="#industryType">选择
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </form>
                            <c:if test="${not empty NOCX and NOCX==true}">
                                <div class="form-group">
                                    <label class="control-label col-lg-2"></label>
                                    <div class="col-lg-8 text-right">
                                        <button class="btn btn-info" id="lawObjectSearchBtn"
                                                style="width: 150px;">查询
                                        </button>
                                    </div>
                                </div>
                            </c:if>
                        </div>

                    </div>
                    <!-- ./smart-widget-inner -->
                </div>
                <!-- ./smart-widget -->
            </div>
        </div>
        <!--./第一层快速查询row-->

        <!--第二层任务办理row-->
        <div class="row">
            <!--任务办理-->
            <div class="col-lg-12">
                <div class="smart-widget-body form-horizontal">
                    <div class="form-group">
                        <label class="control-label col-lg-2"></label>
                        <div class="col-lg-3">&nbsp;
                        </div>
                        <div class="col-lg-7 text-right">

                            <c:if test="${not empty  NOXGSSJ and NOXGSSJ ==true}">
                                <button type="button" class="btn btn-info" onclick="newRandomAttrOnclick()">修改双随机属性
                                </button>
                            </c:if>
                            <c:if test="${not empty NOXZZFDX and NOXZZFDX==true}">
                                <button class="btn btn-info" type="button" id="newEnterpriseObjectBtn">新增执法对象</button>
                            </c:if>
                            <c:if test="${not empty NOSC and NOSC ==true}">
                                <button class="btn btn-info" id="delObjectBtn">删除</button>
                            </c:if>
                            <c:if test="${not empty NODCEXCEL and NODCEXCEL ==true}">
                                <button id="viewExcel" class="btn btn-info" style="width: 100px;margin-right: 10px;">
                                    导出excel
                                </button>
                            </c:if>

                        </div>
                    </div>
                </div>
                <div class="smart-widget widget-blue">
                    <div class="smart-widget-header font-16">
                        <i class="fa fa-comment"></i> 无主 <span
                            class="smart-widget-option"> <span
                            class="refresh-icon-animated"> <i
                            class="fa fa-circle-o-notch fa-spin"></i>
								</span> 
								</span>
                    </div>
                    <div class="smart-widget-inner table-responsive">
                        <table id="staryObjectThreeTable" class="table-no-bordered"></table>
                        <input type="hidden" id="status" value="0">
                    </div>
                </div>
            </div>
            <!--./待办任务-->
        </div>
    </div>
</div>
<!-- 行业类型选择（Modal） -->
<div class="modal fade" id="industryType" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
        </div>
    </div>
</div>
<!-- 双随机属性修改 -->
<div class="modal fade" id="ssjsx" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">

        </div>
    </div>
</div>
<!-- 双随机属性--选择 -->
<div class="modal fade" id="update" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">

        </div>
    </div>
</div>

<script type="text/javascript">

    var pageNumber = 1;
    var pageSize = 15;
    var htmlCity = "<option value=''>请选择</option>";
    var htmlCounty = "<option value=''>请选择</option>";
    //同步：加载行政区划时必须等待数据返回才进行下一步
    $(document).ready(function () {
        $.ajax({
            type: "post",
            url: WEBPATH + "/tArea/chickUserArea",
            dataType: "json",
            async: false,
            data: {},
            success: function (data) {
                if (data.cityStatus == '1') {
                    //省级用户
                    $.ajax({
                        type: "post",
                        url: WEBPATH + "/tArea/cityList",
                        dataType: "json",
                        async: false,
                        success: function (data) {
                            $("#belongCity").append("<option value=''>请选择</option>");
                            $("#belongCountry").append("<option value=''>请选择</option>");
                            $("#randomCity").append("<option value=''>请选择</option>");
                            $("#randomCountry").append("<option value=''>请选择</option>");
                            $.each(data, function (i, item) {
                                $("#randomCity").append("<option value=" + item.code + ">" + item.name + "</option>");
                                $("#belongCity").append("<option value=" + item.code + ">" + item.name + "</option>");
                            });
                        }
                    });
                } else if (data.cityStatus == "2") {
                    //市级用户
                    $("#belongCity").append("<option selected value=" + data.cityCode + ">" + data.cityName + "</option>");
                    $("#randomCity").append("<option value=''>请选择</option>");
                    $("#randomCity").append("<option selected value=" + data.cityCode + ">" + data.cityName + "</option>");
                    $.ajax({
                        type: "post",
                        url: WEBPATH + "/tArea/countyListByCode",
                        dataType: "json",
                        data: {parentCode: data.cityCode},
                        success: function (data) {
                            $("#belongCountry").append("<option value=''>请选择</option>");
                            $("#randomCountry").append("<option value=''>请选择</option>");
                            $.each(data, function (i, item) {
                                $("#belongCountry").append("<option value=" + item.code + "  >" + item.name + "</option>");
                                $("#randomCountry").append("<option value=" + item.code + "  >" + item.name + "</option>");
                            });
                        }
                    });
                } else {
                    //县级用户
                    $("#belongCity").append("<option selected value=" + data.cityCode + ">" + data.cityName + "</option>");
                    $("#belongCountry").append("<option selected value=" + data.countyCode + "  >" + data.countyName + "</option>");
                    $("#randomCity").append("<option value=''>请选择</option>");
                    $("#randomCity").append("<option selected value=" + data.cityCode + ">" + data.cityName + "</option>");
                    $("#randomCountry").append("<option value=''>请选择</option>");
                    $("#randomCountry").append("<option selected value=" + data.countyCode + "  >" + data.countyName + "</option>");
                }
            }
        });
        $.ajax({
            type: "post",
            url: WEBPATH + "/tArea/cityList",
            dataType: "json",
            async: false,
            success: function (data) {
                $("#powerCountry").html(htmlCity);
                $("#powerCity").append("<option value=''>请选择</option>");
                $.each(data, function (i, item) {
                    $("#powerCity").append("<option value=" + item.code + ">" + item.name + "</option>");
                });
            }
        });
        //textpdf

        $("#powerCity").change(function () {
            if ($(this).val() == "") {
                $("#powerCountry option").remove();
                $("#powerCountry").html(htmlCounty);
                return;
            }
            var parentCode = $(this).val();
            $("#powerCountry option").remove();
            $("#powerCity").append("<option value=''>请选择</option>");
            $.ajax({
                type: "post",
                url: WEBPATH + "/tArea/countyListByCode",
                dataType: "json",
                async: false,
                data: {parentCode: parentCode},
                success: function (data) {
                    $("#powerCountry").html(htmlCounty);
                    $.each(data, function (i, item) {
                        $("#powerCountry").append("<option value=" + item.code + "  >" + item.name + "</option>");
                    });
                }
            });
        });
        $("#belongCity").change(function () {
            if ($(this).val() == "") {
                $("#belongCountry option").remove();
                $("#belongCountry").html(htmlCounty);
                return;
            }
            var parentCode = $(this).val();
            $("#belongCountry option").remove();
            $.ajax({
                type: "post",
                url: WEBPATH + "/tArea/countyListByCode",
                dataType: "json",
                async: false,
                data: {parentCode: parentCode},
                success: function (data) {
                    $("#belongCountry").html(htmlCounty);
                    $.each(data, function (i, item) {
                        $("#belongCountry").append("<option value=" + item.code + "  >" + item.name + "</option>");
                    });
                }
            });
        });
        $("#randomCity").change(function () {
            if ($(this).val() == "") {
                $("#randomCountry option").remove();
                $("#randomCountry").html(htmlCounty);
                return;
            }
            var parentCode = $(this).val();
            $("#randomCountry option").remove();
            $.ajax({
                type: "post",
                url: WEBPATH + "/tArea/countyListByCode",
                dataType: "json",
                async: false,
                data: {parentCode: parentCode},
                success: function (data) {
                    $("#randomCountry").html(htmlCounty);
                    $.each(data, function (i, item) {
                        $("#randomCountry").append("<option value=" + item.code + "  >" + item.name + "</option>");
                    });
                }
            });
            if ($(this).val() != "") {
                $.ajax({
                    type: "post",
                    url: WEBPATH + "/tArea/chickUserArea",
                    async: false,
                    dataType: "json",
                    data: {},
                    success: function (data) {
                        if (data.cityStatus != "2" && data.cityStatus != "1") {
                            //县级用户
                            $("#randomCountry option").remove();
                            $("#randomCity option").remove();
                            $("#randomCity").append("<option value=''>请选择</option>");
                            $("#randomCity").append("<option selected value=" + data.cityCode + ">" + data.cityName + "</option>");
                            $("#randomCountry").append("<option value=''>请选择</option>");
                            $("#randomCountry").append("<option selected value=" + data.countyCode + "  >" + data.countyName + "</option>");
                        }
                    }
                });
                return;
            }

        });

        //返回按钮之后参数和页码的回显
        var params = $("#groundBackParams").val();

        if (params != null && params != '' && params != 'undefined') {

            var jsonParam = $.parseJSON(params);

            pageNumber = parseInt(jsonParam['pageNum']);
            pageSize = parseInt(jsonParam['pageSize']);

            for (var key in jsonParam) {
                //绑定设定条件
                if (key == 'belongCity' || key == 'powerCity' || key == 'randomCity') {
                    $("#" + key).find("option[value='" + jsonParam[key] + "']").attr("selected", "selected");
                    //$("#"+key).val(jsonParam[key]);
                    $("#" + key).trigger('change');//值改变之后触发加载事件
                    continue;
                }
                $("#" + key).val(jsonParam[key]);
            }
        }

        //执法对象选择模态框表单动态加载staryObjectThreeTable
        LoadingDataListLawObjctItems();
        $('#staryObjectThreeTable').bootstrapTable('hideColumn', 'id');
        $("#objectName").click(function () {
            LoadingDataListLawObjctItems();
            $('#staryObjectThreeTable').bootstrapTable('hideColumn', 'id');
        })
    });

    function LoadingDataListLawObjctItems() {
        $('#staryObjectThreeTable').bootstrapTable({
            method: 'post',
            dataType: "json",
            url: WEBPATH + '/zfdx/lawObjectList1',
            undefinedText: '-',
            pagination: true, // 分页
            striped: true, // 是否显示行间隔色
            cache: false, // 是否使用缓存
            pageSize: 15, // 设置默认分页为 20
            pageNumber: 1,
            queryParamsType: "",
            pageList: [5, 15, 30, 50, 100, 200], // 自定义分页列表
            singleSelect: false,
            contentType: "application/x-www-form-urlencoded;charset=UTF-8",
            // pageList : [ 5, 10, 20 ],
            // showColumns : true, // 显示隐藏列
            sidePagination: "server", //服务端请求
            queryParams: queryLawObjectParams,//参数
            uniqueId: "id", // 每一行的唯一标识
            //序号	名称	证件号	联系人	联系方式	权属
            columns: [
                {
                    field: "id",
                    title: "id",
                    align: 'center',
                },

                {
                    checkbox: true,
                    title: "操作",
                    align: 'center',
                }, {
                    field: "",
                    title: "名称",
                    align: 'left',
                    formatter: function (value, row, index) {
                        var NOFQXCZF = '${NOFQXCZF}';
                        if (NOFQXCZF) {
                            return "<a href ='#' style ='color:#6CCCF1;' onclick=checkObject(\'" + row.id + "\')> " + row.objectName + "</a>";
                        } else {
                            return row.objectName
                        }
                    }
                },
                {
                    field: "typeCode",
                    title: "类型",
                    align: 'center',
                },
                {
                    field: "cardNumber",
                    title: "证照号",
                    align: 'center',
                },
                {
                    field: "legalPerson",
                    title: "联系人",
                    align: 'center',
                },
                {
                    field: "legalPhone",
                    title: "联系方式",
                    align: 'center',
                },
                {
                    field: "belongAreaName",
                    title: " 所在行政区",
                    align: 'center',
                },
                {
                    field: "powerAreaName",
                    title: "权属行政区",
                    align: 'center',
                }, {
                    field: "randomAttrName",
                    title: "双随机属性",
                    align: 'center',
                },
                {
                    field: "",
                    title: "操作",
                    align: 'center',
                    formatter: function (value, row, index) {
                        var NOFQXCZF = '${NOFQXCZF}';
                        if (NOFQXCZF) {
                            return "<a href ='#' style='color:#6CCCF1;cursor:pointer;' onclick=startTask(\'" + row.id + "\')><i class='fa fa-edit'></i>发起现场执法</a>";
                        } else {
                            return '发起现场执法';
                        }
                    }
                }
            ],
            responseHandler: function (res) {
                return {
                    total: res.total,
                    rows: res.list
                };
            },
            onCheck: function (row, $element) {
            },//单击row事件
            onUncheck: function (row, $element) {
            },
            onRefresh: function () {
            },
            onPreBody: function () {
            },
            formatLoadingMessage: function () {
                return "数据加载中...";
            },
            formatNoMatches: function () { //没有匹配的结果
                return '无符合条件的记录';
            }
        })
    }

    function queryLawObjectParams(params) {
        var objectName = $("#objectName").val();
        var cardNumber = $("#cardNumber").val();
        var status = $("#status").val();
        var belongProvince = $("#belongProvince").val();
        var belongCity = $("#belongCity").val();
        var belongCountry = $("#belongCountry").val();
        var powerProvince = $("#powerProvince").val();
        var powerCity = $("#powerCity").val();
        var powerCountry = $("#powerCountry").val();
        var randomProvince = $("#randomProvince").val();
        var randomCity = $("#randomCity").val();
        var randomCountry = $("#randomCountry").val();
        var selectThis = $("#selectThis").val();
        var isRandom = $("#isRandom").val();
        var randomAttr = $("#randomAttr").val();
        var industryTypeCode = $("#industryTypeCode").val();
        var WSCD = $("#wscd").val();
        var WSNM = $("#wsnm").val();
        //var aa =$("#seachObjectRorm").serialize();
        var temp = {  //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
            //objectName  address  lawObjectType
            pageNum: params.pageNumber,
            pageSize: params.pageSize,
            belongProvince: belongProvince,
            belongCity: belongCity,
            belongCountry: belongCountry,
            powerProvince: powerProvince,
            powerCity: powerCity,
            powerCountry: powerCountry,
            industryTypeCode:industryTypeCode,
            cardNumber: cardNumber,
            objectName: objectName,
            typeCode: "5",
            status: status,
            randomProvince: randomProvince,// 双随机行政区省
            randomCity: randomCity,// 双随机行政区市
            randomCountry: randomCountry,// 双随机行政区县
            selectThis: selectThis,// 只选本级
            isRandom: isRandom,// 是否纳入双随机
            randomAttr: randomAttr, // 双随机属性
            WSCD: WSCD,
            WSNM: WSNM
        };
        return temp;
    }

    //	lawObjectStatus对象条件查询
    $("#lawObjectSearchBtn").click(function () {
        $("#status").val("1");
        $("#staryObjectThreeTable").bootstrapTable('destroy');
        $("#objectName").trigger("click")
        $('#staryObjectThreeTable').bootstrapTable('refresh');
    });

    //=======================双随机部分处理==开始========================

    $("#selectThis").change(function () {
        var selectThis = document.getElementById('selectThis');
        if (selectThis.checked) {
            $("#selectThis").val("1");
        } else {
            $("#selectThis").val("");
        }
    });

    var controlShow_attr = document.getElementById("controlShow_attr");
    var controlShow_area = document.getElementById("controlShow_area");

    //双随机属性框是否显示
    $(document).ready(function () {
        if ($("#isRandom").val() == '1') {
            controlShow_attr.style.display = "block";
            controlShow_area.style.display = "block";
            //return;
        }
        if ($("#selectThis").val() == '1') {
            document.getElementById('selectThis').checked = true;
        }
    });
    $("#isRandom").change(function () {
        var isRandom = $("#isRandom").val();
        if (isRandom == '1') {
            controlShow_attr.style.display = "block";
            controlShow_area.style.display = "block";
            return;
        } else {
            controlShow_attr.style.display = "none";
            controlShow_area.style.display = "none";
            $("#randomAttr").val("");
            return;
        }
    });
    //=======================双随机部分处理==结束========================


    //新增执法对象
    $("#newEnterpriseObjectBtn").click(function () {
        business.addMainContentParserHtml(WEBPATH + '/zfdx/newLawObjectPage?type=5&menuId=' + '${menuId}', null);
    });

    //导出Excel表格

    $("#viewExcel").click(function () {
        window.location.href = WEBPATH + '/zfdx/downExcel?' + $("#searchForm").serialize();
    });

    //查询对象细信息
    function checkObject(id) {
        var menuId = '${menuId}';
        business.addMainContentParserHtml(WEBPATH + '/zfdx/detailedInformationObject?id=' + id + '&typeCode=5' + '&menuId=' + menuId, null);
    }

    //发起现场执法
    function startTask(id) {
        business.addMainContentParserHtml(WEBPATH + '/jcbl/startLocaleChick?lawObjectId=' + id + '&menuId=' + '${menuId}', null);
    }

    //删除执法对象操作
    $("#delObjectBtn").click(function () {
        $('#staryObjectThreeTable')
        var all = $('#staryObjectThreeTable').bootstrapTable('getSelections');
        if (all.length > 0) {
            var arrID = new Array();
            for (var i = 0; i < all.length; i++) {
                arrID.push(all[i].id);
            }
            swal({
                title: "提示 ",
                text: "确定要删除所选择的记录吗？",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "是的，我要删除！",
                cancelButtonText: "让我再考虑一下",
                closeOnConfirm: false,
                closeOnCancel: false
            }, function (isConfirm) {
                if (isConfirm) {
                    $.ajax({
                        type: "post",
                        url: WEBPATH + "/zfdx/delObjectByIds",
                        data: {
                            ids: (arrID.join(","))
                        },
                        dataType: "json",
                        success: function (data) {
                            if (data.meta.statusCode == "200") {
                                business.addMainContentParserHtml(WEBPATH + '/zfdx/zfdx-stray?menuId=' + '${menuId}', null);
                                swal({
                                    title: "提示",
                                    text: data.meta.message,
                                    type: "success",
                                    allowOutsideClick: true
                                });

                            } else if (data.meta.statusCode == "406") {
                                business.addMainContentParserHtml(WEBPATH + '/zfdx/zfdx-stray?menuId=' + '${menuId}', null);
                                swal({
                                    title: "提示",
                                    text: data.meta.message,
                                    type: "info",
                                    allowOutsideClick: true
                                });
                            } else {
                                business.addMainContentParserHtml(WEBPATH + '/zfdx/zfdx-stray?menuId=' + '${menuId}', null);
                                swal({
                                    title: "提示",
                                    text: "删除失败！",
                                    type: "error",
                                    allowOutsideClick: true
                                });
                            }
                        }
                    });
                } else {
                    swal({
                        title: "已取消",
                        text: "您取消了删除操作！",
                        type: "info",
                        allowOutsideClick: true
                    });
                }
            })
        } else {
            swal({
                title: "提示",
                text: "至少选择一条执法对象！",
                type: "error",
                allowOutsideClick: true
            });
        }
    });

    $('.chart').easyPieChart({
        easing: 'easeOutBounce',
        size: '140',
        lineWidth: '7',
        barColor: '#7266ba',
        onStep: function (from, to, percent) {
            $(this.el).find('.percent').text(Math.round(percent));
        }
    });

    $('.sortable-list').sortable();

    $('.todo-checkbox').click(function () {

        var _activeCheckbox = $(this).find('input[type="checkbox"]');

        if (_activeCheckbox.is(':checked')) {
            $(this).parent().addClass('selected');
        } else {
            $(this).parent().removeClass('selected');
        }

    });

    //Delete Widget Confirmation
    $('#deleteWidgetConfirm').popup({
        vertical: 'top',
        pagecontainer: '.container',
        transition: 'all 0.3s'
    });

    //点击添加双随机属性
    function newRandomAttrOnclick() {
        var all = $('#staryObjectThreeTable').bootstrapTable('getSelections');
        var ids = "";
        if (all.length == 0) {
            swal("提示", "请先选择至少一家无主！", "info");
            return false;
        } else {
            $('#ssjsx').on('hide.bs.modal', function () {
                $(this).removeData("bs.modal");
            })
            for (var i = 0; i < all.length; i++) {

                ids = ids + all[i].id + ',';
            }
            ids = ids.substring(0, ids.length - 1);
            // $("#newRandomAttr").attr("data-remote","${webpath}/zfdx/randomAttrModal?ids="+ids);
            var options = {
                remote: WEBPATH + '/zfdx/randomAttrModal?ids=' + ids + '&typeCode=5'
            };
            $('#ssjsx').modal(options);
        }
    }
</script>
<script type="text/javascript">
    $(document).ready(function () {
        //监听enter查询，内包含监听回退键
        business.listenEnter("lawObjectSearchBtn");
    });
</script>
</body>
</html>