<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<script type="text/javascript">
    var flag = '${flag}';

var arraySend=new Array();
$("#fileFile").fileinput({
	theme: "explorer",
	language : 'zh',
	uploadUrl : WEBPATH + '/tmEvidenceCollect/videoOrRecordUpload?fileType=3', // you must set a valid URL here else you will get an error
	/* allowedFileExtensions : [ 'mp4', 'ogg', 'webm','mov'], */
	allowedFileExtensions : [ 'mp4','mov'],
	msgFileTypes:{
        'video': 'video'
    },
	maxFileSize : 1044*50,
	maxFilePreviewSize:1024*50,
	maxFileCount: 6,
	uploadAsync:true,
  	initialCaption: "附件格式支持mp4,mov,每个文件不大于50M,数量为6",
	slugCallback : function(filename) {
		return filename.replace('(', '_').replace(']', '_');
	}
}).on('filebatchuploadsuccess', function(event, data, previewId, index) {
	arraySend=arraySend.concat(data.response.data);
}).on('fileuploaded',function(event,data){
	arraySend=arraySend.concat(data.response.data);
}).on('filesuccessremove', function(event, id) {
  // alert(id);
 });
		
$(document).ready(function(){
	$('#uploadFile').on('hide.bs.modal', function () {
		   $(this).removeData("bs.modal");  
	})
})	

$('#fileOk').on('click',function() {
		if(arraySend.length>0){
				loding('fileOk',"确定");
				$.ajax({
			        type:"post",
			        url:encodeURI(WEBPATH+"/tmEvidenceCollect/xczf-zjcj-spzjOrYpzj-save?taskId="+'${taskId}&fileType=3'),
			        async: false,//使用同步的方式,true为异步方式
			        dataType:"json",
			        contentType:"application/json;charset=utf-8",
			        data:JSON.stringify(arraySend),
			        success:function(data){
			        	if(data.meta.result=="success"){
			        	   $('#UploadAttachments').modal('hide');
		        		   swal({
		                       title : "上传成功！",
		                       text : "附件上传成功",
		                       type : "success"
		                     },function(){
		                    	 business.addMainContentParserHtml(WEBPATH + '/tmEvidenceCollect/xczf-zjcj-spzj',$("#taskObjectForm").serialize()+"&selectType=4&evidenceType=3&flag=" +flag);
		                     })
			        	}else{
			        		$('#UploadAttachments').modal('hide');
			        		swal("上传失败", "附件上传失败", "error");
			        	}
			        }
			  	});
		}else{
			swal("提示", "请选择要上传的附件", "info");
		}
});

</script>
<div class="modal-header">
	<div style="float:right; margin-top:-5px;">
    <button type="button" class="btn btn-info" id="fileOk">确定</button>
	<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
    </div>
	<h4 class="modal-title" id="fileModalLabel">录像</h4>
</div>
<div class="modal-body">
	 <div class="smart-widget-body form-horizontal">
		<div class="form-group"> 
			<label for="任务要求附件" class="col-lg-2 control-label">录像</label>
			<div class="col-lg-9">
				<input id="fileFile" name="files[]" type="file" multiple>
			</div>
		</div>
	</div>
</div>
<div class="modal-footer"> 
	<!--<button type="button" class="btn btn-info" id="fileOk">确定</button>
	<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>-->
</div>