<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
    <%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>  
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<script type="text/javascript">
    var flag = '${flag}';
	$(document).ready(function(){
		$('#myDia').on('hidden.bs.modal', function () {//用hide.bs.modal修改时间之后会出现遮罩
			   $(this).removeData("bs.modal");//清除图片缓存
                evidenceCollect(flag);//重新加载页面，不然点击关闭之后再点开保存按钮失效
		})
		var url=$("#parentUrl").val();
		if(url=='0'){
			$("#picinfoSave").hide();
			$("[name='evidenceObject']").prop("disabled",true);
			$("[name='cameraTime']").prop("disabled",true);
			$("[name='cameraAddress']").prop("disabled",true);
			$("[name='cameraMan']").prop("disabled",true);
			$("[name='party']").prop("disabled",true);
			$("[name='lawEnforcPerson']").prop("disabled",true);
			$("[name='lawEnforcId']").prop("disabled",true);
			
		}else{
			$("#picinfoSave").show();
		}
	})
	$('#picinfoSave').on('click',function() {
			loding('picinfoSave',"保存");
			$("#picinfoForm").data('formValidation').validate();
			var validate = $("#picinfoForm").data('formValidation').isValid();
			var degree = parseInt($("#degree").val());
			if(validate){
				$.ajax({
	                type: "POST",
	                url: WEBPATH+'/tmEvidenceCollect/picinfoUpdates?degree='+degree,
	                data:$('#picinfoForm').serialize(),//  form law object
	                async: false,
	                error: function(request) {
	                	swal("错误!","系统错误", "error");
	                },
	                success: function(data) {
	                	if (data.result == 'success') {// 成功
	                		 $('#myDia').modal('hide');
	                		 setTimeout(null, 200);
	                		swal({ 
								title : "保存成功",
								text : "保存成功",
								type : "success",
								closeOnConfirm: true,
					            confirmButtonText: "确定",
					            confirmButtonColor: "#A7D5EA"
							},function(){
	                		    debugger;
								 setTimeout(null, 200);
								$('#myDia').modal('hide');
								$("#parentUrl").val(1);// 从办理途径进入 0:表示历史途径进入，1：标示编辑途径进入
								business.addMainContentParserHtml(WEBPATH + '/tmEvidenceCollect/xczf-zjcj-zpzj',$("#taskObjectForm").serialize()+"&selectType=4");
							});
	            		}else{
	            			 $('#myDia').modal('hide')
		            		 swal({title:"保存失败!",text:data.data, type:"error",allowOutsideClick :true});
	            		}
	                }
	            });
				return true;
			}
	 		
			
	});
	 //执法证号生效日期
	$("[name='cameraTime']").datetimepicker({
	        format:'yyyy-mm-dd',
	        todayBtn: true,
	        autoclose: true,
	        minView:'year',
	        maxView:'decade'
	 }).on('changeDate', function(ev){
		  $('#picinfoForm').formValidation('revalidateField', 'cameraTime');
	 });
</script>
<div class="modal-content">
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"
            aria-hidden="true">&times;</button>
        <h4 class="modal-title" id="picSS">编辑</h4>
    </div>
    <div class="modal-body">
        <div class="smart-widget-body">
        	<div class="row">
                <div class="col-md-8 col-sm-8"  id="img2017">
                    <div class="pricing-value">
	                    <div class="changneng" style="text-align: center;">
	                       <!--  <p id="szh"> -->
	                          <img id="image" src="${FASTDFS_ADDR}/${picinfoObj.picUrl}"/>
	                        <!-- </p> -->
	                    </div>
                    </div>
                </div>
                <input type="button" value="向←旋转" onclick="leftClick()" id="rotationLeft"/>
                    <input type="button" value="向→旋转" id="rotationRight" onclick="rightClick()"/>
                <div class="col-md-4 col-sm-4">
                    <div class="form-group text-right">
						<button type="button" class="btn btn-info" id="picinfoSave">保存</button>
						<a href="${webpath}/tmEvidenceCollect/picDownLoad?id=${picinfoObj.id}" download="w3logo"><button type="button" class="btn btn-info">下载</button></a>
                    </div>
                   <form id="picinfoForm">
                    <input type="hidden" name="id" value="${picinfoObj.id}">
                    <input type="hidden" id="picUrl" name="picUrl" value="${picinfoObj.picUrl}"/>
                    <input type="hidden" id="fileId" name="fileId" value="${picinfoObj.fileId}"/>
                    <div class="form-group">
                        <label class="control-label">证明对象</label> <input type="text" title="${picinfoObj.evidenceObject}"
                            value="${picinfoObj.evidenceObject}" name="evidenceObject" placeholder="证明对象" class="form-control"
                        data-parsley-required="true">
                    </div>
                    <div class="form-group">
                        <label class="control-label">拍摄时间</label> <input type="text" readonly
                            value="<fmt:formatDate value="${picinfoObj.cameraTime}" pattern="yyyy-MM-dd"/>" name="cameraTime" placeholder="拍摄时间" class="form-control"
                            data-parsley-required="true">
                    </div> 
                    <div class="form-group">
                        <label class="control-label">拍摄地点</label> <input type="text" title="${picinfoObj.cameraAddress}"
                            value="${picinfoObj.cameraAddress}" name="cameraAddress" placeholder="拍摄地点" class="form-control" data-parsley-required="true">
                    </div>
                    <div class="form-group">
                        <label class="control-label">拍摄人</label> <input type="text" title="${picinfoObj.cameraMan}"
                            value="${picinfoObj.cameraMan}" name="cameraMan" placeholder="拍摄人" class="form-control" data-parsley-required="true">
                        </div>
                        <div class="form-group">
                            <label class="control-label">当事人、见证人</label> <input type="text" title="${picinfoObj.party}"
                                value="${picinfoObj.party}" name="party" placeholder="当事人、见证人" class="form-control"
                                data-parsley-required="true">
                        </div>
                        <div class="form-group">
                        	<c:choose>
                            	<c:when test="${empty picinfoObj.lawEnforcPerson}">
                            		 <label class="control-label">执法人员</label> <input type="text" title="${task.checUserNames}"
                                value="${task.checUserNames}" name="lawEnforcPerson" placeholder="执法人员" class="form-control" data-parsley-required="true">
                            	</c:when>
                            	<c:otherwise>
                            		<label class="control-label">执法人员</label> <input type="text" title="${picinfoObj.lawEnforcPerson}"
                                value="${picinfoObj.lawEnforcPerson}" name="lawEnforcPerson" placeholder="执法人员" class="form-control" data-parsley-required="true">
                            		
                            	</c:otherwise>
                            </c:choose>
                        </div>
                        <div class="form-group">
                        	<c:choose>
                            	<c:when test="${empty picinfoObj.lawEnforcId}">
                            	<label class="control-label">执法证号</label> <input type="text" title="${task.lawEnforcIds}"
                                value="${task.lawEnforcIds}" name="lawEnforcId" placeholder="执法证号" class="form-control" data-parsley-required="true">
                            			
                            	</c:when>
                            	<c:otherwise>
                            	<label class="control-label">执法证号</label> <input type="text" title="${picinfoObj.lawEnforcId}"
                                value="${picinfoObj.lawEnforcId}" name="lawEnforcId" placeholder="执法证号" class="form-control" data-parsley-required="true">
                            	</c:otherwise>
                            </c:choose>
                            
                        </div>
                        </form>
                </div>
            </div>
        </div>
	</div>
    <div class="modal-footer">
    <input type="hidden" id="jiaoduR" value="0">
    <input type="hidden" id="jiaoduL" value="360">
    <input type="hidden" id="degree" value="0">
        <!--<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>-->
    </div>
</div>
<script type="text/javascript">
    
	$(document).ready(function(){
		var img = document.getElementById("image");
		if(img!=null&& img!='' && typeof(img)!='undefined'){
			var dx = img.width;
		    var dy = img.heigth;
		    if(dx>dy){
			    img.style.width="100%";
		    }else{
			    img.style.height="100%";
		    }
		}
	})
	function rotateRight(){
		   var x = document.getElementById("image");
		  // var p = document.getElementById("szh");
		   n=parseInt($("#jiaoduR").val());
		   n = n + 90;
		   
		   x.style.transform = "rotate(" + n + "deg)";
		   x.style.webkitTransform = "rotate(" + n + "deg)";
		   x.style.OTransform = "rotate(" + n + "deg)";
		   x.style.MozTransform = "rotate(" + n + "deg)";
		   var dx = x.width;
		   var dy = x.heigth;
		   if(dx>dy){
			   x.style.width="100%";
		   }else{
			   x.style.height="100%";
		   }
		   if (n >= 360){
		   		n = 0;
		   }
		   $("#jiaoduR").val(n);
		   $("#degree").val(n/90);
	}   
	function rightClick(){
		rotateRight();
		
	}

	function leftClick(){
		rotateLeft();
	}

	function rotateLeft(){
		   var x = document.getElementById("image");
		   //var p = document.getElementById("szh");
		   n=parseInt($("#jiaoduL").val());
		   n = n - 90;
		  
		   x.style.transform = "rotate(" + n + "deg)";
		   x.style.webkitTransform = "rotate(" + n + "deg)";
		   x.style.OTransform = "rotate(" + n + "deg)";
		   x.style.MozTransform = "rotate(" + n + "deg)";
		   var dx = x.width;
		   var dy = x.heigth;
		   if(dx>dy){
			   x.style.width="100%";
		   }else{
			   x.style.height="100%";
		   }
		   if (n <= 0){
		   		n = 360;
		   }
		   $("#jiaoduL").val(n);
		   $("#degree").val(n/90);
	} 
//限制输入字符长度
$("#picinfoForm").formValidation({
    framework: 'bootstrap',
    message: 'This value is not valid',
    icon:{
            valid: 'glyphicon glyphicon-ok',
            invalid: 'glyphicon glyphicon-remove',
            validating: 'glyphicon glyphicon-refresh'
           },
    fields: {
    	evidenceObject: {
    		message: '不能超过60个字符',
            validators: {
                stringLength: {  
                min: 0,  
                max: 60,  
                message: '不能超过60个字'  
                }
        	}
    	},
    	cameraTime: {
    		message: '不能超过60个字符',
            validators: {
                stringLength: {  
                min: 0,  
                max: 60,  
                message: '不能超过60个字'  
                }
        	}
    	},
    	cameraAddress: {
    		message: '不能超过60个字符',
            validators: {
                stringLength: {  
                min: 0,  
                max: 60,  
                message: '不能超过60个字'  
                }
        	}
    	},
    	cameraMan: {
    		message: '不能超过30个字符',
            validators: {
                stringLength: {  
                min: 0,  
                max: 30,  
                message: '不能超过30个字'  
                }
        	}
    	},
    	party: {
    		message: '不能超过30个字符',
            validators: {
                stringLength: {  
                min: 0,  
                max: 30,  
                message: '不能超过30个字'  
                }
        	}
    	},
    	lawEnforcPerson: {
    		message: '不能超过1000个字符',
            validators: {
                stringLength: {  
                min: 0,  
                max: 1000,  
                message: '不能超过1000个字'  
                }
        	}
    	},
    	lawEnforcId : {
    		message: '不能超过1000个字符',
            validators: {
                stringLength: {  
                min: 0,  
                max: 1000,  
                message: '不能超过1000个字'  
                }
        	}
		}
    	
    }
});
</script>

<style>
   .changneng {
    width: 100%;
    height: 500px;
    overflow: hidden;
    position: relative;
    margin-top: 10px;
}

.changneng > p {
    position: absolute;
    cursor: move;
    transform-origin: center;
    -webkit-transform-origin: center;
    -moz-transform-origin: center;
    -ms-transform-origin: center;
    -o-transform-origin: center;
    width: 100%;
    height: 100%;
    padding: 0;
    -webkit-margin-before: 0;
    -webkit-margin-after: 0;
    cursor: move;
    left: 0;
    top: 0;
}

.changneng > p > img {
    display: inline-block;
    vertical-align: middle;
    cursor: move;
   }
</style>