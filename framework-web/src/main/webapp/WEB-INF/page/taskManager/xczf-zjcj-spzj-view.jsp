<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@page
	import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%
	String fastdfs_addr = PropertiesHandlerUtil.getValue("fastdfs.nginx.ip", "fastdfs");
%>
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<link href="${webpath }/static/css/plyr.css" rel="stylesheet" />
<script type="text/javascript" src='${webpath }/static/js/plyr.js'></script>
<script type="text/javascript">
	$(document).ready(function(){
		$('#player').on('hidden.bs.modal', function () {//用hide.bs.modal修改时间之后会出现遮罩
			   $(this).removeData("bs.modal");//清除图片缓存
			   evidenceCollectVideo();//重新加载页面，不然点击关闭之后再点开保存按钮失效
		})
	})
	"use strict";
	plyr.setup();
</script>

<div class="modal-content">
	<div class="modal-header">
		<div style="float: right; margin-top: -5px;">
			<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
		</div>
		<h4 class="modal-title" id="myModalLabel">视频播放</h4>
	</div>
	<div class="modal-body">
		<div class="smart-widget-body">
			<div class="row">
				<div class="col-md-8">
					<div class="pricing-value" style="height: 100%">
						<span class="value">
							<div class="plyr">
								<video controls preload="meta">
									<source src="${FASTDFS_ADDR}/${videoInfoObj.videoUrl}"	type="video/webm">
								</video>
							</div>
						</span>
					</div>
				</div>
				<div class="col-md-4">
					<ul class="text-left padding-sm" >
						<li>证明对象：${videoInfoObj.evidenceObject}</li>
						<li>拍摄时间：<fmt:formatDate value='${videoInfoObj.videoTime}' pattern="yyyy-MM-dd"/></li>
						<li>拍摄地点：${videoInfoObj.videoAddress}</li>
						<li>拍摄人：${videoInfoObj.videoMan}</li>
						<li>当事人、见证人：${videoInfoObj.party}</li>
						<li>执法人员：${videoInfoObj.lawEnforcPerson}</li>
						<li>执法证号：${videoInfoObj.lawEnforcId}</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</div>
