<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<html>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"  type="text/javascript"></script>

<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>  
<c:set var="fastdfs_addr"><%=fastdfs_addr%></c:set>
<body>
	 <div class="modal-header">
             <div style="float:right; margin-top:-5px;">
                        <button type="button" class="btn btn-info"  onclick="saveModel()" >确定</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
             </div>
              <h4 class="modal-title" id="myModalLabel">本地头像上传</h4>
                </div>
                <div class="modal-body">
                        <div class="smart-widget-body form-horizontal padding-md">                    
                            <div class="form-group">
                                <div class="col-lg-12">
                                    <form enctype="multipart/form-data">
                                        <input id="file-es" name="aws_upload_file" type="file" multiple>
                                    </form>
                                                                                             
                                </div>
                            </div>
    
    
                        </div>
                    </div>
     
	
	<script type="text/javascript">
	var imgPath = null;
	$(document).ready(function(){
		console.log("nihao...");
		/* $('#file-es').fileinput({
			language : 'es',
			uploadUrl : '#',
			allowedFileExtensions : [ 'jpg', 'png', 'gif', 'pdf' ],
		}); */
		$('#file-es').fileinput({
			language : 'zh',
			uploadUrl : WEBPATH + '/tmEvidenceCollect/picupload', // you must set a valid URL here else you will get an error
			allowedFileExtensions : [ 'jpg', 'png', 'bmp'],
			maxFileSize : 1024*10,
			maxFilePreviewSize:1024*10,
			maxFileCount: 1,
			uploadAsync:true,
		  	initialCaption: "附件格式支持png，jpg，bmp，每个文件不大于10M，数量为1",
			slugCallback : function(filename) {
				return filename.replace('(', '_').replace(']', '_');
			},
			showClose:false,
			showRemove:false,
			initialPreviewCount:1,
			initialPreviewDelimiter:'*$$*',
			initialPreviewFileType:'image',
			//initialPreview:preList,
			initialPreviewConfig: [
			    {
			        width: '320px',
			        key: 100,
			        extra: {id: 100}
			    }
			]
		}).on('filebatchuploadsuccess', function(event, data, previewId, index) {
			$("#avatarUrl").val(data.response.data[0].fileUrl);
			$("#fileId").val(data.response.data[0].id);
		}).on('fileuploaded',function(event,data){
			imgPath=data.response.data[0].fileUrl;
			$("#avatarUrl").val(data.response.data[0].fileUrl);
			$("#fileId").val(data.response.data[0].id);
			$("#defaultAvatarUrl").val("");
			$("#isDefaultAvatar").val("");
		}).on('filedeleted', function(event, data, previewId, index) {
		});

	});
	function saveModel(){
		if(imgPath != null){
			var path = '${fastdfs_addr}'+imgPath;
			$("#imgPath").attr('src',path);
		}
		
		$('#computerView').modal('hide');
	};
	$(function(){
		
	});
	</script>
</body>
</html> 