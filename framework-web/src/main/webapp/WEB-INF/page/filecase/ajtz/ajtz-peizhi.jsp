<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<script
	src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"
	type="text/javascript"></script>
<title>Insert title here</title>
</head>
<body class="overflow-hidden">
	<input id="groundBackParams" name="groundBackParams" type="hidden" value='${params }'/><!-- 返回所需的参数 -->
	<div class="main-container">
		<div class="padding-md">
			<!--执行处罚台账row-->
			<div class="row">
				<div class="col-md-12">
					<div class="smart-widget widget-blue">
						<div class="smart-widget-header font-16">
							<i class="fa fa-arrow-right"></i> 推荐台账 <span
								class="smart-widget-option"> <span
								class="refresh-icon-animated"> <i
									class="fa fa-circle-o-notch fa-spin"></i>
							</span>
							</span>
						</div>
						<div class="smart-widget-inner table-responsive">
							<div class="smart-widget-body form-horizontal padding-lg">
								<div class="col-lg-4 col-md-4 col-sm-2 col-xs-4">
                                     <div class="statistic-box bg-info m-bottom-md" onClick="xzcfClick()" style="cursor:pointer;">
                                         <div class="statistic-title">
                                             <h4>行政处罚</h4>
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-lg-4 col-md-4 col-sm-2 col-xs-4">
                                     <div class="statistic-box bg-info m-bottom-md  padding-md" onClick="xzmlClick()" style="cursor:pointer;">
                                         <div class="statistic-title">
                                            <h4>行政命令</h4>
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-lg-4 col-md-4 col-sm-2 col-xs-4">
                                     <div class="statistic-box bg-info m-bottom-md  padding-md" onClick="cfkyClick()" style="cursor:pointer;">
                                         <div class="statistic-title">
                                             <h4>查封扣押</h4>
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-lg-4 col-md-4 col-sm-2 col-xs-4">
                                     <div class="statistic-box bg-info m-bottom-md  padding-md" onClick="xctcClick()" style="cursor:pointer;">
                                         <div class="statistic-title">
                                             <h4>限产停产</h4>
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-lg-4 col-md-4 col-sm-2 col-xs-4">
                                     <div class="statistic-box bg-info m-bottom-md  padding-md" onClick="xzjlClick()" style="cursor:pointer;">
                                         <div class="statistic-title">
                                             <h4>行政拘留</h4>
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-lg-4 col-md-4 col-sm-2 col-xs-4">
                                     <div class="statistic-box bg-info m-bottom-md  padding-md" onClick="hjwrfzClick()" style="cursor:pointer;">
                                         <div class="statistic-title">
                                             <h4>环境污染犯罪</h4>
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-lg-4 col-md-4 col-sm-2 col-xs-4">
                                     <div class="statistic-box bg-info m-bottom-md  padding-md" onClick="qtysClick()" style="cursor:pointer;">
                                         <div class="statistic-title">
                                             <h4>其他移送</h4>
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-lg-4 col-md-4 col-sm-2 col-xs-4">
                                     <div class="statistic-box bg-info m-bottom-md  padding-md" onClick="arjfClick()" style="cursor:pointer;">
                                         <div class="statistic-title">
                                             <h4>按日计罚</h4>
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-lg-4 col-md-4 col-sm-2 col-xs-4">
                                     <div class="statistic-box bg-info m-bottom-md  padding-md" onClick="qzzxClick()" style="cursor:pointer; ">
                                         <div class="statistic-title">
                                             <h4>申请法院强制执行</h4>
                                         </div>
                                     </div>
                                 </div>
                                 <div class="col-lg-4 col-md-4 col-sm-2 col-xs-4">
                                     <div class="statistic-box bg-info m-bottom-md  padding-md" onClick="bigCaseClick()" style="cursor:pointer; ">
                                         <div class="statistic-title">
                                             <h4>综合案件</h4>
                                         </div>
                                     </div>
                                 </div>
                                <div class="col-lg-4 col-md-4 col-sm-2 col-xs-4">
                                    <div class="statistic-box bg-info m-bottom-md  padding-md" onClick="qqcfCaseClick()" style="cursor:pointer; ">
                                        <div class="statistic-title">
                                            <h4>发改委对接台账</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-4 col-sm-2 col-xs-4">
                                    <div class="statistic-box bg-info m-bottom-md  padding-md" onClick="qqcfCaseClick()" style="cursor:pointer; ">
                                        <div class="statistic-title">
                                            <h4>亲情处罚台账推送列表</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>

<script type="text/javascript">
$(function(){
    //监听回退键
    business.listenBackSpace();    
   });
	$(document).ready(function(){
		//返回按钮之后参数和页码的回显
		//var params = $("#groundBackParams").val();
		var params = '${params}';
		if(params != null && params != '' && params != 'undefined'){
			
			var jsonParam = $.parseJSON(params);
			
			for(var key in jsonParam){
				//绑定设定条件
				$("[name="+key+"]").val(jsonParam[key]);
			}
		}
	})
	
	//行政处罚
	function xzcfClick(){
		business.addMainContentParserHtml(WEBPATH + '/ajtz/toAdministrativePage', null);
	}
	
	//行政命令
	function xzmlClick(){
		business.addMainContentParserHtml(WEBPATH + '/ajtz/toExcutiveOrderPage', null);
	}
	
	//行政拘留
	function xzjlClick(){		  
		business.addMainContentParserHtml(WEBPATH + '/ajtz/xzjlParameterHomePage', null);
	}
	//其他移送
		function qtysClick(){		  
			business.addMainContentParserHtml(WEBPATH + '/ajtz/qtysParameterHomePage', null);
		}

	//推荐台账-查封扣押
	function cfkyClick(){
		business.addMainContentParserHtml(WEBPATH + '/ajtz/toSequestrationPage', null);
	}
	
	//限产停产
	function xctcClick(){
		business.addMainContentParserHtml(WEBPATH + '/ajtz/toLimitStopPage', null);
	}
	
	//按日计罚
	function arjfClick(){
		business.addMainContentParserHtml(WEBPATH + '/ajtz/toPenaltyPage', null);
	}
	
	//推荐台账-环境污染犯罪
	function hjwrfzClick(){
		business.addMainContentParserHtml(WEBPATH + '/ajtz/toPollutionCrimePage', null);
	}
	//推荐台账-申请法院强制执行
	function qzzxClick(){
		business.addMainContentParserHtml(WEBPATH + '/ajtz/toApplyForcePage', null);
	}
	//推荐台账-综合案件
	function bigCaseClick(){
		business.addMainContentParserHtml(WEBPATH + '/ajtz/toCasePage', null);
	}
	//推荐台账-发改委
	function fgwCaseClick(){
		business.addMainContentParserHtml(WEBPATH + '/ajtz/aj-fgw', null);
	}
    //推荐台账-亲情处罚
    function qqcfCaseClick(){
       business.addMainContentParserHtml(WEBPATH + '/ajtz/aj-qqcf', null);
    }

</script>
</html>