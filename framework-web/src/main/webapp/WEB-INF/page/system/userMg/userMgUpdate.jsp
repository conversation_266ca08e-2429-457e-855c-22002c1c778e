<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>用户添加</title>
<script>
$(function() {
	
	$('#setPower').on('hide.bs.modal', function () {
		   $(this).removeData("bs.modal");  
	})
	var belongDepartmentId;
	var belongDepartmentName;
	var setting = {
		    data: {
		      key: {
		        title:"t"
		      },
		      simpleData: {
		        enable: true
		      }
		    },
		    callback: {
				onClick: function(event, treeId, treeNode){
					belongDepartmentId=treeNode.id;
					belongDepartmentName=treeNode.name;
				}
			}
	};
	
	$('#depok').on('click',function() {
			if(belongDepartmentId==""||typeof(belongDepartmentId)== "undefined"){//zj添加，打开模态框直接点击确定的时候判断此判断
				belongDepartmentId = '${updateUser.belongDepartmentId}';
				belongDepartmentName = '${updateUser.belongDepartmentName}';
			}else{
				$("[name='belongDepartmentId']").val(belongDepartmentId);
				$("[name='belongDepartmentName']").val(belongDepartmentName);
			}
			$('#updateForm').formValidation('revalidateField', 'belongDepartmentName');
			$('#setPower').modal('hide');
	 		$.ajax({
                type: "POST",
                url: WEBPATH+'/sysUser/loadRole',
                data:{id:belongDepartmentId},
                async: false,
                error: function(request) {
                	swal("错误!","系统错误", "error");
                },
                success: function(data) {
                	if (data.meta.result == 'success') {// 成功
                		var hasRole=$.parseJSON('${hasRoel}');
                		console.info(data.data);
                	    var html="";
                		for (var i = 0; i<data.data.length; i++) {
                			/* var div1="<div class='custom-checkbox'>"+
                			"<input type='checkbox' name='roles' data-fv-field='roles' value='"+data.data[i].roleId+"' id='roleDesc"+i+"'>"+
                			"<label for='roleDesc"+i+"' class='checkbox-blue'></label>"+
                			"</div>"+
                			"<div class='inline-block vertical-top'>"+data.data[i].roleRealName+"</div>";
                			html+=div1+"&nbsp;&nbsp;&nbsp;"; */
                			var div1="<div class='custom-checkbox'>"+
		        			"<input type='checkbox' name='roles' ";
		        			for (var j = 0; j < hasRole.length; j++) {
								if(hasRole[j].roleId==data.data[i].roleId){
									div1+="checked='checked'";
								}
							}
		        			div1+=" value='"+data.data[i].roleId+"' id='roleDesc"+i+"'>"+
		        			"<label for='roleDesc"+i+"' class='checkbox-blue'></label>"+
		        			"</div>"+
		        			"<div class='inline-block vertical-top'>"+data.data[i].roleRealName+"</div>";
		        			html+=div1+"&nbsp;&nbsp;&nbsp;";
						}
                		$("#roleDiv").html(html);
                		$("#updateForm").formValidation('addField', "roles");
            		}else{
	            		 swal("选择失败!","选择部门失败", "error");
            		}
                }
            });
	});	
	
	$('#setPower2').on('hide.bs.modal', function () {
		   $(this).removeData("bs.modal");  
	})
	var orgBelongDepartId;
	var orgBelongDepartName;
	var setting2 = {
		    data: {
		      key: {
		        title:"t"
		      },
		      simpleData: {
		        enable: true
		      }
		    },
		    callback: {
				onClick: function(event, treeId, treeNode){
					orgBelongDepartId=treeNode.id;
					orgBelongDepartName=treeNode.name;
				}
			}
	};
	
	$('#depok2').on('click',function() {
		if(orgBelongDepartId==""||typeof(orgBelongDepartId)== "undefined"){//zj添加，打开模态框直接点击确定的时候判断此判断
			orgBelongDepartId = '${updateUser.orgBelongDepartId}';
			orgBelongDepartName = '${updateUser.orgBelongDepartName}';
		}else{
			$("[name='orgBelongDepartId']").val(orgBelongDepartId);
			$("[name='orgBelongDepartName']").val(orgBelongDepartName);
		}
		$('#updateForm').formValidation('revalidateField', 'orgBelongDepartName');
		$('#setPower2').modal('hide');
	});			
	
	
	var zNodes;
	$.ajax({
      type: "post",
      url: WEBPATH+"/sysDept/getDepts",
      success: function (strReult) {
        if(strReult.type=="success"){
          zNodes=eval(strReult.data);
          $.fn.zTree.init($("#treeDept"), setting, zNodes);
          var treeObj = $.fn.zTree.getZTreeObj("treeDept");
          var belongDepartmentId_sel = '${updateUser.belongDepartmentId}';
          var selNode = treeObj.getNodeByParam("id",belongDepartmentId_sel, null);
          treeObj.selectNode(selNode);//选择点 
        }else{
          swal("服务异常，部门数据请求失败!", "", "warning");
        }
      },
      error: function(){
           swal("网络异常，请求数据失败!", "", "error");
      }
	});
	
	var zNodes2;
	$.ajax({
      type: "post",
      url: WEBPATH+"/sysDept/getDepts",
      success: function (strReult) {
        if(strReult.type=="success"){
        	zNodes2=eval(strReult.data);
            $.fn.zTree.init($("#treeDept2"), setting2, zNodes2); 
            
            var treeObj = $.fn.zTree.getZTreeObj("treeDept2");
            var orgBelongDepartId_sel = '${updateUser.orgBelongDepartId}';
            var selNode = treeObj.getNodeByParam("id",orgBelongDepartId_sel, null);
            treeObj.selectNode(selNode);//选择点 
        }else{
          swal("服务异常，部门数据请求失败!", "", "warning");
        }
      },
      error: function(){
           swal("网络异常，请求数据失败!", "", "error");
      }
	});
	
});


$(document).ready(function(){
	 //执法证号生效日期
	 $("[name='lawEffectiveDate']").datetimepicker({
	        format:'yyyy/mm/dd',
	        todayBtn: true,
	        autoclose: true,
	        minView:'year',
	        maxView:'decade'
	 }).on('changeDate', function(ev){
		  //$('#updateForm').formValidation('revalidateField', 'lawEffectiveDate');
	 });
	 //执法证号失效日期
	 $("[name='lawInvalidDate']").datetimepicker({
	        format:'yyyy/mm/dd',
	        todayBtn: true,
	        autoclose: true,
	        minView:'year',
	        maxView:'decade'
	 }).on('changeDate', function(ev){
		 //$('#updateForm').formValidation('revalidateField', 'lawInvalidDate');
	 });
	 //监察证号生效日期
	 $("[name='supervisionEffectiveDate']").datetimepicker({
	        format:'yyyy/mm/dd',
	        todayBtn: true,
	        autoclose: true,
	        minView:'year',
	        maxView:'decade'
	 }).on('changeDate', function(ev){
		  //$('#updateForm').formValidation('revalidateField', 'supervisionEffectiveDate');
	 });
	 //监察证号失效日期
	 $("[name='supervisionInvalidDate']").datetimepicker({
	        format:'yyyy/mm/dd',
	        todayBtn: true,
	        autoclose: true,
	        minView:'year',
	        maxView:'decade'
	 }).on('changeDate', function(ev){
		  //$('#updateForm').formValidation('revalidateField', 'supervisionInvalidDate');
	 });
	//出生日期
	 $("[name='birthday']").datetimepicker({
	        format:'yyyy-mm-dd',
	        language:'cn',
	        todayBtn: true,
	        clearBtn : true,
	        autoclose: true,
	        endDate : new Date(),
	        minView:'year',
	        maxView:'decade'
	 }).on('changeDate', function(ev){
		 $('#updateForm').formValidation('revalidateField', 'birthday');
	 });
	 //从事执法工作开始年份
	 $("[name='startLawEnforceJobYear']").datetimepicker({
	        format:'yyyy',
	        language:'cn',
	        autoclose: true,
	        endDate : new Date(),
	        startView:'decade',
	        minView:'decade',
	        maxView:'year'
	 }).on('changeDate', function(ev){
	 });
	 
	//表单非空验证
   $("#updateForm").formValidation({
       framework: 'bootstrap',
       message: 'This value is not valid',
       icon:{
	            valid: 'glyphicon glyphicon-ok',
	            invalid: 'glyphicon glyphicon-remove',
	            validating: 'glyphicon glyphicon-refresh'
              },
       fields: {
       	 /*  province: {
               message: '省份不能为空',
               validators: {
                   notEmpty: {
                       message: '省份不能为空'
                   }
           	}
           
          },
          city: {
               message: '市不能为空',
               validators: {
                   notEmpty: {
                       message: '市不能为空'
                   }
           	}
           
          },
          county: {
               message: '县不能为空',
               validators: {
                   notEmpty: {
                       message: '县不能为空'
                   }
           	}
           
          }, */
          belongDepartmentName: {
               message: '所属部门不能为空',
               validators: {
                   notEmpty: {
                       message: '所属部门不能为空'
                   }
           	}
           
         },
         username: {
               message: '登录用户名已经存在',
               validators: {
                   notEmpty: {
                       message: '登录用户名不能为空'
                   },  //远程异步校验
                   stringLength: {
                       min: 5,
                       max: 30,
                       message: '用户名为5-30个字符'
                   },
                   regexp: {
                       /* regexp: /^[a-zA-Z0-9|\-]+$/,
                       message: '用户名由字母、数字中划线组成' */
                	   regexp: /^[a-zA-Z0-9_\.\-]+$/,
                       message: '用户名由字母、数字、中划线、下划线和点组成'
                   },
                   remote:{
                       url: WEBPATH+'/sysUser/checkLoginnameIsExistForupdate',
                       type: 'POST',
                       data:{username:$("[name='username']").val(),updateid:$("[name='id']").val()}
                  }
           	}
           
         },
         password: {
               message: '登录密码不能为空',
               validators: {
                   stringLength: {
                       min: 6,
                       max: 15,
                       message: '原密码为6-15个字符'
                   },
                   regexp: {
                       regexp: /^[a-zA-Z0-9_\.]+$/,
                       message: '原密码由字母、数字、下划线或. 组成'
                   }
           	}
           
         },
          loginname: {
               message: '姓名不能为空',
               validators: {
                   notEmpty: {
                       message: '姓名不能为空'
                   },
                   stringLength: {
                       max: 100,
                       message: '姓名最大长度为100个字符'
                   },
                   regexp: {
                       regexp: /^\S+$/,
                       message: '名字不能出现空格'
                   }
           	}
           
         },
         cardid: {
               validators: {
                   notEmpty: {
                       message: '身份证号不能为空'
                   },
        		   stringLength: {
                       min: 15,
                       max: 18,
                       message: '身份证号位15-18个字符'
                   },
                   regexp: {
                       regexp: /^\S+$/,
                       message: '身份证号不能出现空格'
                   }
               }
          },
          phone: {
               validators: {
               	 	notEmpty: {
	                        message: '手机号码不能为空'
	                 },
	                 phone:{
	                      message:'请输入正确的手机号码',
	                       country:'CN'
	                 }
               }
          },
          jobId: {
               validators: {
               	 notEmpty: {
	                        message: '职务不能为空'
	                 }
               }
          },
          birthday: {
              validators: {
              	 notEmpty: {
                          message: '出生日期不能为空'
                   }
              }
         },
          gendar: {
              validators: {
              	 notEmpty: {
                          message: '性别不能为空'
                   }
              }
         },
          accountNonTeam: {
               validators: {
               	 notEmpty: {
	                        message: '是否纳入队伍管理不能为空'
	                 }
               }
          },
          accountNonLawofficer: {
               validators: {
               	 notEmpty: {
	                        message: '是否双随机执法人员不能为空'
	                 }
               }
          },
          accountNonStation: {
              validators: {
              	 notEmpty: {
                          message: '是否在编人员不能为空'
                   }
              }
         },
         accountNonPost: {
             validators: {
             	 notEmpty: {
                         message: '是否在岗人员不能为空'
                  }
             }
        },
          orgBelongDepartName: {
               validators: {
               	 notEmpty: {
	                        message: '编制所属部门不能为空'
	                 }
               }
          },
         /*  lawDeviceId: {
               validators: {
               	 notEmpty: {
	                        message: '执法设备ID不能为空'
	                 }
               }
          }, */
          lawEnforcId: {
               validators: {
               	 notEmpty: {
	                        message: '执法证号不能为空'
	                 },
	                 callback: {
	                    	message: '',
	                        callback: function(value, validator, $field) {
	                        	var val=$("input[name='lawCertificateEnabled']:checked").val();
	                        	if(val==1){
	                        		$("#updateForm").formValidation('enableFieldValidators', 'lawEnforcId', true);
	                        	}else{
	                        		$("#updateForm").formValidation('enableFieldValidators', 'lawEnforcId', false);
	                        	}
	        					return true;
	                        } 
	                 } 
               }
          },
          /* lawEffectiveDate: {
               validators: {
               	 notEmpty: {
	                        message: '生效日期不能为空'
	                 }
               }
          },
          lawInvalidDate: {
               validators: {
               	 notEmpty: {
	                        message: '失效日期不能为空'
	                 }
               }
          }, */
         	lawCertificateEnabled: {
               validators: {
	               	 notEmpty: {
		                        message: '执法证是否启用不能为空'
		             },
	                 callback: {
	                    	message: '',
	                        callback: function(value, validator, $field) {
	                        	var val=$("input[name='lawCertificateEnabled']:checked").val();
	                        	if(val==1){
	                        		$("#updateForm").formValidation('enableFieldValidators', 'lawEnforcId', true);
	                        	}else{
	                        		$("#updateForm").formValidation('enableFieldValidators', 'lawEnforcId', false);
	                        	}
	        					return true;
	                        } 
	                 } 
               }
          },
          supervisionCertificateId: {
               validators: {
               	 notEmpty: {
	                        message: '监察证号不能为空'
	                 },
	                 callback: {
		                 	message: '',
		                     callback: function(value, validator, $field) {
		                     	var val=$("input[name='supervisioCertificateEnabled']:checked").val();
		                     	if(val==1){
		                     		$("#updateForm").formValidation('enableFieldValidators', 'supervisionCertificateId', true);
		                     	}else{
		                     		$("#updateForm").formValidation('enableFieldValidators', 'supervisionCertificateId', false);
		                     	}
		     					return true;
		                     }
		              	} 
               }
          },
        /*   supervisionEffectiveDate: {
               validators: {
               	 notEmpty: {
	                        message: '生效日期不能为空'
	                 }
               }
          },
          supervisionInvalidDate: {
               validators: {
               	 notEmpty: {
	                        message: '失效日期不能为空'
	                 }
               }
          } , */
          supervisioCertificateEnabled: {
               validators: {
	               	 notEmpty: {
		                        message: '监察证是否启用不能为空'
		             },
	                 callback: {
	                 	message: '',
	                     callback: function(value, validator, $field) {
	                     	var val=$("input[name='supervisioCertificateEnabled']:checked").val();
	                     	if(val==1){
	                     		$("#updateForm").formValidation('enableFieldValidators', 'supervisionCertificateId', true);
	                     	}else{
	                     		$("#updateForm").formValidation('enableFieldValidators', 'supervisionCertificateId', false);
	                     	}
	     					return true;
	                     }
	              	} 
               }
          } ,
          roles: {
              validators: {
              	 notEmpty: {
	                        message: '角色不能为空'
	                 }
              }
         }
      }       
   }); 
   //表单提交
   $('#updateBtn').click(function() {
	   				$("#updateForm").data('formValidation').validate();
	   			    $('#updateForm').formValidation('revalidateField', 'lawEnforcId');
	   			    $('#updateForm').formValidation('revalidateField', 'supervisionCertificateId');
		            var validate = $("#updateForm").data('formValidation').isValid();
		            var options = {
				             url:WEBPATH+'/sysUser/updateUser',
				             type: 'post',
				             success:function(data){
				            	 if(data.meta.result=='success'){
				            		 //business.addMainContentParserHtml(WEBPATH+'/sysUser/userMain',null);
				   	        	  	 //swal({title: "保存成功",text: "",type:"success"});
				   	        	  	 
				   	        	  	 var preUrl = '${preUrl }';
									 if(preUrl != null && preUrl != '' && preUrl != 'undefined'){
						   	   			if(preUrl=='/sysUser/userMain'){
						   	   				business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1&menuId=4AE820E38F2F6585E055000000000001", $("#searchForm").serialize());
						   	   			}else{
						   	   				business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1&menuId="+'${menuId}', $("#searchForm").serialize());
						   	   			}
						   	   			swal({title: "保存成功",text: "",type:"success",allowOutsideClick :true});
						   	   		 } else {
						   	   			business.addMainContentParserHtml(WEBPATH+'/sysUser/userMain',null);
				   	        	  	 	swal({title: "保存成功",text: "",type:"success",allowOutsideClick :true});
						   	   		 }
				   	        	  	 
				            	 }else if(data.meta.code == '007'){
				            		 swal({ title : data.meta.message, text : "", type : "info" ,allowOutsideClick :true});
				            	 }else{
				            		 swal({title:"保存失败!", text:"信息保存失败", type:"error",allowOutsideClick :true});
				            	 }
				            }
				     };
		             if(validate){
		            	 var flag=false; 
		            	 $("input[type=checkbox][name=roles]").each(function (i, e) {
		                       if ($(this).is(':checked')) {
		                    	   flag=true;
		                       }
		                 });
		            	 if(flag){
		            		 loding('updateBtn',"保存");
			            	 hiddenVal();
					         $('#updateForm').ajaxSubmit(options);
					       	 return true;
		            	 }else{
		            		 swal({title: "角色不能为空",text: "",type:"error",allowOutsideClick :true});
		            		 return false;
		            	 }
		            	
		             }else{
		            	 $("#updateForm").data('formValidation').validate();
		            	 return false;
                    } 
          
   }); 
   setJobId();
});

function hiddenVal(){
	var provinceCode=$("#province").val();
	var cityCode=$("#city").val();
	var countyCode=$("#county").val();
	var provinceText=$("#province").find("option:selected").text();
	var cityText=$("#city").find("option:selected").text();
	var countyText=$("#county").find("option:selected").text();
	if(provinceCode!=""&&cityCode!=""&&countyCode!=""){
		$("[name='belongAreaId']").val(countyCode);
		$("[name='belongAreaName']").val(provinceText);
	}else if(countyCode==""&&provinceCode!=""&&cityCode!=""){
		if(provinceCode=="11000000"||provinceCode=="12000000"||provinceCode=="31000000"||provinceCode=="55000000"){
			$("[name='belongAreaId']").val(cityCode);
			$("[name='belongAreaName']").val(cityText);
		}else{
			$("[name='belongAreaId']").val(cityCode);
			$("[name='belongAreaName']").val(cityText);
		}
	}else if(countyCode==""&&cityCode==""&&provinceCode!=""){
		$("[name='belongAreaId']").val(provinceCode);
		$("[name='belongAreaName']").val(countyText);
	}else{
		$("[name='belongAreaId']").val("");
		$("[name='belongAreaName']").val("");
	}
}

function setJobId(){
	$("[name='jobName']").val($("#jobId").find("option:selected").text());
}
</script>
</head>
<body>
<div class="main-container">
	<div class="padding-md">

				<!--第一层row-->
				<div class="row">					
					<div class="col-lg-12">
						<div class="smart-widget widget-blue">
							<div class="smart-widget-header font-16">
								<i class="fa fa-comment"></i> 用户信息<span
									class="smart-widget-option"> <span
									class="refresh-icon-animated"> <i
										class="fa fa-circle-o-notch fa-spin"></i>
								</span> 
								</span>
							</div>
							<div class="smart-widget-inner table-responsive">
								<div class="smart-widget-body form-horizontal">
								<form id="updateForm">
										<input  name="token" value="${tokenReport }" type="hidden">
										<input type="hidden" name="id"  value="${updateUser.id}"/>
										<legend class="font-16">基本信息</legend>
                                        <div style="float:right; margin-top:-55px;"><button class="btn btn-info" style="width:100px;" type="button" onclick="goBack('${preUrl}')">返回</button></div>
										<%-- <div class="form-group">
											<label for="行政区划" class="col-lg-2 control-label"><i
												style="color: red;">*</i> &nbsp;&nbsp;行政区划</label>
														<div class="col-lg-2">
														<c:set var="provinceStrStart" value="${fn:substring(updateUser.belongAreaId,0,2)}" />
		                 								<c:set var="provinceCode" value="${provinceStrStart}000000"/>
														<select id="province" name="province" <c:if test="${tarea.arealevel==0}">onchange="business.cascaded($('#province').val(),'city');" </c:if>  class="form-control" style="margin-right:2px;">
										                 <c:forEach var="province" items="${provinceList }" >
										                 	<option <c:if test="${tarea.arealevel==1||tarea.arealevel==2||tarea.arealevel==3}">selected</c:if> <c:if test="${province.code==provinceCode}">selected</c:if>    value="${province.code}" >${province.name}</option>
										                 </c:forEach>
										               </select>
													</div>
													<div class="col-lg-2">
														<c:choose>
									                 		<c:when test="${provinceStrStart==11||provinceStrStart==12||provinceStrStart==31||provinceStrStart==55}">
									                 			<c:set var="cityStrStart" value="${fn:substring(updateUser.belongAreaId,0,6)}" />
									                 			<c:set var="cityCode" value="${cityStrStart}00"/>
									                 		</c:when>
									                 		<c:otherwise>
									                 			<c:set var="cityStrStart" value="${fn:substring(updateUser.belongAreaId,0,4)}" />
									                 			<c:set var="cityCode" value="${cityStrStart}0000"/>
									                 		</c:otherwise>
									                 	</c:choose>
														<select id="city" name="city" <c:if test="${tarea.arealevel==0 || tarea.arealevel==1}">onchange="business.cascaded($('#city').val(),'county');" </c:if>  class="form-control" style="margin-right:2px;">
										                	<c:if test="${tarea.arealevel!=2||tarea.arealevel!=3}">
										                		<option value="">——市级——</option>
										                	</c:if>
											                 <c:forEach var="city" items="${ cityList}" >
											                 	<option <c:if test="${tarea.arealevel==2||tarea.arealevel==3}">selected</c:if> <c:if test="${cityCode==city.code}">selected</c:if>  value="${city.code}">${city.name}</option>
											                 </c:forEach>
										               </select>
													</div>
													<div class="col-lg-2">
													<c:if test="${provinceStrStart!=11&&provinceStrStart!=12&&provinceStrStart!=31&&provinceStrStart!=55}">
							                 			<c:set var="countyCode" value="${updateUser.belongAreaId}"/>
							                 		</c:if>
														<select id="county" name="county" class="form-control" >
											                 <c:if test="${tarea.arealevel!=3}">
											                 	<option value="">——县级——</option>
											                 </c:if>
											                 <c:forEach var="county" items="${countyList }" >
											                 	<option <c:if test="${tarea.arealevel==3}">selected</c:if> <c:if test="${countyCode==county.code}">selected</c:if>  value="${county.code}">${county.name}</option>
											                 </c:forEach>
											            </select>
													</div>
											 <input type="hidden" name="belongAreaId" value="${updateUser.belongAreaId}"/>
											 <input type="hidden" name="belongAreaName" value="${updateUser.belongAreaName}"/>
											 
										</div> --%>
										<div class="form-group">
											<label for="所属部门" class="col-lg-2 control-label"><span style="color:red;">*</span> 所属部门</label>
											<div class="col-lg-6">
												<div class="input-group">
													<input type="text" readonly  name="belongDepartmentName" value="${updateUser.belongDepartmentName }" class="form-control" placeholder="所属部门">
													<input type="hidden" name="belongDepartmentId" value="${updateUser.belongDepartmentId}"/>
													<div class="input-group-btn">
														<button type="button" class="btn btn-info no-shadow"
															tabindex="-1" data-toggle="modal" data-target="#setPower">所属部门添加</button>
													</div>
												</div>
											</div>
										</div>
										<div class="form-group">
											<label for="登录用户名" class="col-lg-2 control-label"><i
												style="color: red;">*</i> &nbsp;&nbsp;登录用户名</label>
											<div class="col-lg-6">
												<input class="form-control" name="username" value="${updateUser.username}"   placeholder="登录用户名" />
											</div>
										</div>
										<div class="form-group">
											<label for="登录密码" class="col-lg-2 control-label"> &nbsp;&nbsp;登录密码</label>
											<div class="col-lg-6">
												<input class="form-control" type="password" name="password"  placeholder="此项为非必填项，需要修改密码时请输入最新密码" />
											</div>
										</div>
										<div class="form-group">
											<label for="姓名" class="col-lg-2 control-label"><i
												style="color: red;">*</i> &nbsp;&nbsp;姓名</label>
											<div class="col-lg-6">
												<input class="form-control" name="loginname" value="${updateUser.loginname}" placeholder="姓名" />
											</div>
										</div>
										<div class="form-group">
											<label for="身份证号" class="col-lg-2 control-label"><i
												style="color: red;">*</i> &nbsp;&nbsp;身份证号</label>
											<div class="col-lg-6">
												<input class="form-control" name="cardid" value="${updateUser.cardid}" placeholder="身份证号码"  />
											</div>
										</div>
										<div class="form-group">
											<label for="手机号码" class="col-lg-2 control-label"><i
												style="color: red;">*</i> &nbsp;&nbsp;手机号码</label>
											<div class="col-lg-6">
												<input class="form-control" name="phone" value="${updateUser.phone}" placeholder="手机号码" />
											</div>
										</div>
										<div class="form-group">
											<label for="职务" class="col-lg-2 control-label"><i
												style="color: red;">*</i> &nbsp;&nbsp;职务</label>
											<div class="col-lg-6">
												<select class="form-control" id="jobId" name="jobId" onchange="setJobId()">
													<option value="">请选择</option>
													<c:forEach items="${jobs}" var="job" varStatus="status">
													<option <c:if test="${updateUser.jobId==job.id}">selected</c:if> value="${job.id }">${job.jobname}</option>
													</c:forEach>
												</select>
												<input type="hidden" name="jobName"/>
											</div>
										</div>
										<div class="form-group">
											<label for="学历" class="col-lg-2 control-label">
												&nbsp;&nbsp;学历</label>
											<div class="col-lg-6">
												<select class="form-control" id="education" name="education">
													<option value="">请选择</option>
													<c:forEach items="${educations}" var="education">
														<option <c:if test="${updateUser.education==education.code}">selected</c:if> value="${education.code}">${education.name}</option>
													</c:forEach>
												</select>
												<!-- <input class="form-control" name="education" placeholder="学历" /> -->
											</div>
										</div>
										<div class="form-group">
											<label for="出生日期" class="col-lg-2 control-label"><i
													style="color: red;">*</i>&nbsp;&nbsp;出生日期</label>
											<div class="col-lg-6">
												<input class="form-control" readonly id="birthday" name="birthday" value="<fmt:formatDate value="${updateUser.birthday}" pattern="yyyy-MM-dd"/>" placeholder="年-月-日" />
											</div>
										</div>
										<div class="form-group">
												<label for="性别" class="col-lg-2 control-label"><i
													style="color: red;">*</i> &nbsp;&nbsp;性别</label>
												<div class="col-lg-6">
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="gendarman" name="gendar" value="1" <c:if test="${updateUser.gendar==1}">checked</c:if>>
															<label for="gendarman"></label>
														</div>
														<div class="inline-block vertical-top">男</div>
													</div>
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="gendarwoman" name="gendar" value="2" <c:if test="${updateUser.gendar==2}">checked</c:if>>
															<label for="gendarwoman"></label>
														</div>
														<div class="inline-block vertical-top">女</div>
													</div>
												</div>
										</div>
										<legend class="font-16">用户信息</legend>
										<div class="form-group">
											<label for="是否纳入用户管理" class="col-lg-2 control-label"><i
												style="color: red;">*</i> &nbsp;&nbsp;是否纳入用户管理</label>
											<div class="col-lg-6" style="padding-top: 8px;">
												<span>是 </span>
											</div>
											<input type="hidden" name="accountNonSystem" value="1"/>
										</div>
										<div class="form-group">
											<label for="用户编号" class="col-lg-2 control-label"><i
												style="color: red;">*</i> &nbsp;&nbsp;用户编号</label>
											<div class="col-lg-6" style="padding-top: 8px;">
												<span>${updateUser.sysUserNumber}</span>
												<input type="hidden" name="sysUserNumber"  value="${updateUser.sysUserNumber}">
											</div>
										</div>
										<div class="form-group">
											<label for="登录昵称" class="col-lg-2 control-label"><i
												style="color: red;">*</i> &nbsp;&nbsp;登录昵称</label>
											<div class="col-lg-6" style="padding-top: 8px;">
												<span>${updateUser.loginNickname}</span>
											</div>
										</div>
										<div class="form-group">
											<label for="角色" class="col-lg-2 control-label"><i
												style="color: red;">*</i> &nbsp;&nbsp;角色</label>
												<div class="col-lg-6">
													<div class="checkbox inline-block" style="padding-right: 30px;" id="roleDiv">
														<%-- <c:forEach  items="${hasRoel}" var="has" varStatus="status2">
															<c:choose>
																<c:when test="${has.roleId==role.roleId}">
																	<div class="custom-checkbox">
																	<input type="checkbox" checked="checked"  name="roles" value="${role.roleId}" id="roleDesc${status.index}"> 
																	<label
																		for="roleDesc${status.index}" class="checkbox-blue"></label>
																	</div>
																	<div class="inline-block vertical-top">${role.roleDesc}</div>
																</c:when>
																<c:otherwise>
																	<div class="custom-checkbox">
																	<input type="checkbox" name="roles" value="${role.roleId}" id="roleDesc${status.index}"> 
																	<label
																		for="roleDesc${status.index}" class="checkbox-blue"></label>
																	</div>
																	<div class="inline-block vertical-top">${role.roleDesc}</div>
																</c:otherwise>
															</c:choose>
														</c:forEach> --%>
													</div>
											</div>
										</div>
										<legend class="font-16">执法队伍信息</legend>
										<div class="form-group"  ${SFNRDWGL}>
												<label for="是否纳入队伍管理" class="col-lg-2 control-label"><i
													style="color: red;">*</i> &nbsp;&nbsp;是否纳入队伍管理</label>
												<div class="col-lg-6">
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="accountNonTeamYes" value="1" <c:if test="${updateUser.accountNonTeam==1}">checked</c:if> name="accountNonTeam">
															<label for="accountNonTeamYes"></label>
														</div>
														<div class="inline-block vertical-top">是</div>
													</div>
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="accountNonTeamNo" value="0" <c:if test="${updateUser.accountNonTeam==0}">checked</c:if> name="accountNonTeam">
															<label for="accountNonTeamNo"></label>
														</div>
														<div class="inline-block vertical-top">否</div>
													</div>
												</div>
										</div>
                                        	<div class="form-group">
                                                <label for="编制所属部门" class="col-lg-2 control-label"><span style="color:red;">*</span> 编制所属部门</label>
                                                <div class="col-lg-6">
                                                    <div class="input-group">
                                                        <input type="text" readonly name="orgBelongDepartName" value="${updateUser.orgBelongDepartName}" class="form-control" placeholder="编制所属部门">
														<input type="hidden" name="orgBelongDepartId" value="${updateUser.orgBelongDepartId}"/>                                                       
                                                        <div class="input-group-btn">
                                                            <button type="button" class="btn btn-info no-shadow"
                                                                tabindex="-1" data-toggle="modal" data-target="#setPower2">编制所属部门添加</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- 需要权限控制  开始  -->
											<div class="form-group"   ${SFSSJZFRY}>
												<label for="是否双随机执法人员" class="col-lg-2 control-label"><i
													style="color: red;">*</i> &nbsp;&nbsp;是否双随机执法人员</label>
												<div class="col-lg-6">
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="accountNonLawofficerYes" value="1"  <c:if test="${updateUser.accountNonLawofficer==1}">checked</c:if> name="accountNonLawofficer">
															<label for="accountNonLawofficerYes"></label>
														</div>
														<div class="inline-block vertical-top">是</div>
													</div>
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="accountNonLawofficerNo"  value="0"  <c:if test="${updateUser.accountNonLawofficer==0}">checked</c:if> name="accountNonLawofficer">
															<label for="accountNonLawofficerNo"></label>
														</div>
														<div class="inline-block vertical-top">否</div>
													</div>
												</div>
											</div>	
											<div class="form-group"  ${SFZBRY}>
												<label for="是否在编人员" class="col-lg-2 control-label"><i
													style="color: red;">*</i> &nbsp;&nbsp;是否在编人员</label>
												<div class="col-lg-6">
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="accountNonStationYes"  <c:if test="${updateUser.accountNonStation==1}">checked</c:if> name="accountNonStation" value="1">
															<label for="accountNonStationYes"></label>
														</div>
														<div class="inline-block vertical-top">是</div>
													</div>
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="accountNonStationNo"   <c:if test="${updateUser.accountNonStation==0}">checked</c:if> name="accountNonStation" value="0">
															<label for="accountNonStationNo"></label>
														</div>
														<div class="inline-block vertical-top">否</div>
													</div>
												</div>
											</div>
											<!-- 2017-07-05 新增字段 开始 -->
											
											<div class="form-group"  ${SFZGRY}>
												<label for="是否在岗人员" class="col-lg-2 control-label"><i
													style="color: red;">*</i> &nbsp;&nbsp;是否在岗人员</label>
												<div class="col-lg-6">
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="accountNonPostYes" name="accountNonPost" <c:if test="${updateUser.accountNonPost==1}">checked</c:if> value="1">
															<label for="accountNonPostYes"></label>
														</div>
														<div class="inline-block vertical-top">是</div>
													</div>
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="accountNonPostNo" name="accountNonPost"  <c:if test="${updateUser.accountNonPost==0}">checked</c:if> value="0">
															<label for="accountNonPostNo"></label>
														</div>
														<div class="inline-block vertical-top">否</div>
													</div>
												</div>
											</div>
											<!-- 需要权限控制  结束 2017-10-18  -->	
																		
											<!-- 2017-07-05 新增字段 结束 -->															
                                            <div class="form-group" >
												<label for="执法设备ID" class="col-lg-2 control-label">
													&nbsp;&nbsp;执法设备ID</label>
												<div class="col-lg-6">
													<input class="form-control" name="lawDeviceId" value="${updateUser.lawDeviceId}" placeholder="执法设备ID" />
												</div>
											</div>
											<div class="form-group">
												<label for="执法证号" class="col-lg-2 control-label">
													&nbsp;&nbsp;执法证号</label>
												<div class="col-lg-6">
													<input class="form-control" name="lawEnforcId" value="${updateUser.lawEnforcId}" placeholder="执法证号" />
												</div>
											</div>
											<div class="form-group">
												<label for="生效日期" class="col-lg-2 control-label">
													&nbsp;&nbsp;生效日期</label>
												<div class="col-lg-6">
													<input class="form-control" name="lawEffectiveDate"  value="<fmt:formatDate value="${updateUser.lawEffectiveDate}" pattern="yyyy/MM/dd"/>"  placeholder="年/月/日" />
												</div>
											</div>
											<div class="form-group">
												<label for="失效日期" class="col-lg-2 control-label">
													&nbsp;&nbsp;失效日期</label>
												<div class="col-lg-6">
													<input class="form-control" name="lawInvalidDate"  value="<fmt:formatDate value="${updateUser.lawInvalidDate}" pattern="yyyy/MM/dd"/>" placeholder="年/月/日" />
												</div>
											</div>
											<div class="form-group">
												<label for="执法证是否启用" class="col-lg-2 control-label">
												<i style="color: red;">*</i>	&nbsp;&nbsp;执法证是否启用</label>
												<div class="col-lg-6">
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="lawCertificateEnabledYes" value="1"  <c:if test="${updateUser.lawCertificateEnabled==1}">checked</c:if> name="lawCertificateEnabled">
															<label for="lawCertificateEnabledYes"></label>
														</div>
														<div class="inline-block vertical-top">是</div>
													</div>
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="lawCertificateEnabledNo" value="0"  <c:if test="${updateUser.lawCertificateEnabled==0}">checked</c:if> name="lawCertificateEnabled">
															<label for="lawCertificateEnabledNo"></label>
														</div>
														<div class="inline-block vertical-top">否</div>
													</div>
												</div>
											</div>
											<div class="form-group">
												<label for="监察证号" class="col-lg-2 control-label">
													&nbsp;&nbsp;监察证号</label>
												<div class="col-lg-6">
													<input class="form-control" name="supervisionCertificateId" value="${updateUser.supervisionCertificateId}" placeholder="监察证号" />
												</div>
											</div>
											<div class="form-group">
												<label for="生效日期" class="col-lg-2 control-label">
													&nbsp;&nbsp;生效日期</label>
												<div class="col-lg-6">
													<input class="form-control" name="supervisionEffectiveDate" value="<fmt:formatDate value="${updateUser.supervisionEffectiveDate}" pattern="yyyy/MM/dd"/>" placeholder="年/月/日" />
												</div>
											</div>
											<div class="form-group">
												<label for="失效日期" class="col-lg-2 control-label">
													&nbsp;&nbsp;失效日期</label>
												<div class="col-lg-6">
													<input class="form-control" name="supervisionInvalidDate" value="<fmt:formatDate value="${updateUser.supervisionInvalidDate}" pattern="yyyy/MM/dd"/>"  placeholder="年/月/日" />
												</div>
											</div>
											<div class="form-group">
												<label for="监察证是否启用" class="col-lg-2 control-label">
													<i style="color: red;">*</i>&nbsp;&nbsp;监察证是否启用</label>
												<div class="col-lg-6">
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="supervisioCertificateEnabledYes" value="1"  <c:if test="${updateUser.supervisioCertificateEnabled==1}">checked</c:if>  name="supervisioCertificateEnabled">
															<label for="supervisioCertificateEnabledYes"></label>
														</div>
														<div class="inline-block vertical-top">是</div>
													</div>
													<div class="radio inline-block">
														<div class="custom-radio m-right-xs">
															<input type="radio" id="supervisioCertificateEnabledNo" value="0"  <c:if test="${updateUser.supervisioCertificateEnabled==0}">checked</c:if>  name="supervisioCertificateEnabled">
															<label for="supervisioCertificateEnabledNo"></label>
														</div>
														<div class="inline-block vertical-top">否</div>
													</div>
												</div>
											</div>
											<div class="form-group">
												<label for="从事执法工作开始年份" class="col-lg-2 control-label">&nbsp;&nbsp;从事执法工作开始年份</label>
												<div class="col-lg-6">
													<input class="form-control" readonly id="startLawEnforceJobYear" name="startLawEnforceJobYear" value="${updateUser.startLawEnforceJobYear}" placeholder="年" />
												</div>
											</div>
											<div class="form-group">
												<label for="擅长执法行业" class="col-lg-2 control-label">&nbsp;&nbsp;擅长执法行业</label>
												<div class="col-lg-6">
													<textarea  class="form-control" rows="2" id="goodAtLawEnforceIndustry" name="goodAtLawEnforceIndustry" placeholder="填写的多个行业请用逗号隔开，最多50个字" maxlength="50">${updateUser.goodAtLawEnforceIndustry}</textarea>
												</div>
											</div>
									</form>
								</div>
                            
                                <div class="modal-footer">
                                    <button class="btn btn-info" style="width:100px;" id="updateBtn" type="button">保存</button>
                                    
                                </div>
							</div>
						</div>
					</div>
				</div>
				<!--./第一层row-->
	</div>
</div>


<!-- 角色权限设置（Modal） -->
	<div class="modal fade" id="setPower" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title" id="myModalLabel">部门列表</h4>
				</div>
				<div class="modal-body">
					<div class="smart-widget-body form-horizontal">
							<div class="form-group">
									<div class="col-lg-6">

										<div >
                                            <ul id="treeDept" class="ztree"></ul>
										</div>
									</div>
							</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-info" data-dismiss="modal" id="depok">确定</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				</div>

			</div>
		</div>
	</div>
	<!-- 设置角色权限（Modal） -->
		<div class="modal fade" id="setPower2" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title" id="myModalLabel">部门列表</h4>
				</div>
				<div class="modal-body">
					<div class="smart-widget-body form-horizontal">
							<div class="form-group">
									<div class="col-lg-6">

										<div >
                                            <ul id="treeDept2" class="ztree"></ul>
										</div>
									</div>
							</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-info" data-dismiss="modal" id="depok2">确定</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				</div>

			</div>
		</div>
	</div>
</body>
<script>
  $(function() {
	if('${cityList.size()}'<=0){
		//初始化地市
		business.cascaded('${provinceCode}','city','${cityCode}');
	}
	if('${countyList.size()}'<=0){
		//初始化区县
		business.cascaded('${cityCode}','county','${countyCode}');
	}
  });
  $(function() {
		/* var setting = {
			    data: {
			      key: {
			        title:"t"
			      },
			      simpleData: {
			        enable: true
			      }
			    },
			    callback: {
					onClick: setDepInfo
				}
		};
		function setDepInfo(event, treeId, treeNode) {
			$("[name='belongDepartmentId']").val(treeNode.id);
			$("[name='belongDepartmentName']").val(treeNode.name);
			$('#updateForm').formValidation('revalidateField', 'belongDepartmentName');
		};
		
		var setting2 = {
			    data: {
			      key: {
			        title:"t"
			      },
			      simpleData: {
			        enable: true
			      }
			    },
			    callback: {
					onClick: setDepInfo2
				}
		};
		function setDepInfo2(event, treeId, treeNode) {
			$("[name='orgBelongDepartId']").val(treeNode.id);
			$("[name='orgBelongDepartName']").val(treeNode.name);
			$('#updateForm').formValidation('revalidateField', 'orgBelongDepartName');
		};
		
		
		var zNodes;
		$.ajax({
	      type: "post",
	      url: WEBPATH+"/sysDept/getDepts",
	      success: function (strReult) {
	        if(strReult.type=="success"){
	          zNodes=eval(strReult.data);
	          $.fn.zTree.init($("#treeDept"), setting, zNodes);
	        }else{
	          swal("服务异常，部门数据请求失败!", "", "warning");
	        }
	      },
	      error: function(){
	           swal("网络异常，请求数据失败!", "", "error");
	      }
		});
		
		var zNodes2;
		$.ajax({
	      type: "post",
	      url: WEBPATH+"/sysDept/getDepts",
	      success: function (strReult) {
	        if(strReult.type=="success"){
	        	zNodes2=eval(strReult.data);
	            $.fn.zTree.init($("#treeDept2"), setting2, zNodes2);         
	        }else{
	          swal("服务异常，部门数据请求失败!", "", "warning");
	        }
	      },
	      error: function(){
	           swal("网络异常，请求数据失败!", "", "error");
	      }
		}); */
		var hasRole=$.parseJSON('${hasRoel}');
		$.ajax({
	        type: "POST",
	        url: WEBPATH+'/sysUser/loadRole',
	        data:{id:$("[name='belongDepartmentId']").val()},
	        async: false,
	        error: function(request) {
	        	swal("错误!","系统错误", "error");
	        },
	        success: function(data) {
	        	if (data.meta.result == 'success') {// 成功
	        	    var html="";
	        		for (var i = 0; i<data.data.length; i++) {
	        					var div1="<div class='custom-checkbox'>"+
			        			"<input type='checkbox' name='roles' ";
			        			for (var j = 0; j < hasRole.length; j++) {
									if(hasRole[j].roleId==data.data[i].roleId){
										div1+="checked='checked'";
									}
								}
			        			div1+=" value='"+data.data[i].roleId+"' id='roleDesc"+i+"'>"+
			        			"<label for='roleDesc"+i+"' class='checkbox-blue'></label>"+
			        			"</div>"+
			        			"<div class='inline-block vertical-top'>"+data.data[i].roleRealName+"</div>";
			        			html+=div1+"&nbsp;&nbsp;&nbsp;";
					}
	        		$("#roleDiv").html(html);
	        		$("#updateForm").formValidation('addField', "roles");
	    		}else{
	        		 swal("加载失败!","选择部门加载部门下的角色失败", "error");
	    		}
	        }
	    });
		
	});
  
//返回上一步主菜单
	function goBack(preUrl) {
		if(preUrl != null && preUrl != '' && preUrl != 'undefined'){
			if(preUrl=='/sysUser/userMain'){
				business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1&menuId=4AE820E38F2F6585E055000000000001", $("#searchForm").serialize());
			}else{
				business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1&menuId="+'${menuId}', $("#searchForm").serialize());
			}
			
		} else {
			swal({
				title : "提示！",
				text : "返回信息错误，请刷新后重试。",
				type : "error"
			})
		}
	}
</script>
</html>