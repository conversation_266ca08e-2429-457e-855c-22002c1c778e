<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"  isELIgnored="false"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%	
	String current_version=PropertiesHandlerUtil.getValue("current_version","version");
	String current_version_date=PropertiesHandlerUtil.getValue("current_version_date","version");
	String downloadAppUrl=PropertiesHandlerUtil.getValue("downloadAppUrl","version");
	String fjServer=PropertiesHandlerUtil.getValue("fjServer","sns_server");
	String snsServer=PropertiesHandlerUtil.getValue("outerSnsServer","sns_server");
%>  
<c:set var="CURRENT_VERSION"><%=current_version%></c:set>
<c:set var="CURRENT_VERSION_DATE"><%=current_version_date%></c:set>
<c:set var="downloadAppUrl"><%=downloadAppUrl%></c:set>
<c:set var="fjServer"><%=fjServer%></c:set>
<c:set var="snsServer"><%=snsServer%></c:set>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%
	String publicKey = PropertiesHandlerUtil.getValue("publicKey", "jiguang"); %>
<c:set var="publicKey"><%=publicKey%></c:set>
<!DOCTYPE html>
<html lang="en">
  	<head>
	    <meta charset="utf-8">
	    <title>福建省生态云环境执法系统</title>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	    <meta name="renderer" content="webkit">
		<meta http-equiv="pragma" content="no-cache">
		<meta http-equiv="cache-control" content="no-cache">
        
        <!-- Bootstrap core CSS -->
	    <link rel="stylesheet" type="text/css" href="${webpath}/static/libs/bootstrap/3.3.4/css/bootstrap.css">
	    <%-- <link href="${webpath }/static/css/bootstrap.min.css" rel="stylesheet" type="text/css"/> --%>
        
        <!-- Font Awesome -->
        <link rel="stylesheet" type="text/css" href="${webpath }/static/font-awesome/4.7.0/css/font-awesome.min.css">

		<%-- <link href="${webpath}/static/css/simplify.min.css" rel="stylesheet"> --%>
        <!--<link href="${webpath}/static/css/style.css" rel="stylesheet">-->
        <link href="${webpath}/static/css/login.css" rel="stylesheet">
        <link href="${webpath}/static/css/components-md.css" rel="stylesheet">
        <link href="${webpath }/static/css/bootstrap-table.min.css" rel="stylesheet">
        
        <!-- sweetalert  -->
		<link href="${webpath }/static/css/sweetalert.css" rel="stylesheet">
        
        <link rel="shortcut icon" href="${webpath }/static/img/zhn_favicon.ico" />
        
        <link href="${webpath }/static/css/formValidation.min.css" rel="stylesheet" type="text/css"/>
        <style type="text/css">
        
		body{
			font-family:"微软雅黑";
		}

		a:link {
			color: #FFF;
			text-decoration: none;
		}
        </style>
		
	    <!-- Le javascript
	    ================================================== -->
	    <!-- Placed at the end of the document so the pages load faster -->
		
		<!-- Jquery -->
		<script src="${webpath }/static/jquery/2.1.1/jquery.min.js"></script>
		
		<!-- sweetalert -->
		<script src="${webpath }/static/js/sweetalert.min.js"></script>
		
		<!-- Bootstrap -->
	   <script src="${webpath }/static/libs/bootstrap/3.3.4/js/bootstrap.min.js"></script>
		
		<!-- Slimscroll -->
		<script src='${webpath}/static/js/jquery.slimscroll.min.js'></script>
		
		<!-- Popup Overlay -->
		<script src='${webpath}/static/js/jquery.popupoverlay.min.js'></script>

		<!-- Modernizr -->
		<script src='${webpath}/static/js/modernizr.min.js'></script>
		
		<script type="text/javascript" src="${webpath}/static/common/js/business.js"></script>
		
		<!-- Simplify -->
		<script src="${webpath}/static/js/simplify/simplify.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/formValidation.min.js"></script>
		<script type="text/javascript" src="${webpath }/static/js/bootstrap.min.js"></script>		
		<script type="text/javascript" src="${webpath }/static/js/jquery.form.js"></script>
		<script src="${webpath }/static/js/bootstrap-table.min.js"></script>
		<script src="${webpath }/static/js/bootstrap-table-zh-CN.js"></script>
		<script type="text/javascript" src ='${webpath }/static/js/qrcode.min.js'></script>
		<script type="text/javascript" src ='${webpath }/static/js/jquery.qrcode.min.js'></script>
  	</head>
  	<body>
    
    <!--<div style="position:absolute; top:12%; font-size:40px; color:#666; width:100%; text-align:center;">福建省环境监察执法平台 <span style="color:red;">（测试环境）</span> </div>-->
    <div style="position:absolute; left: 50%; margin-left: -200px; top:5%;"><img src="${webpath }/static/img/login-logo.png" style="width: 80%;" /></div>
    <div class="login">
        <div class="login_div">
            	<div class="form-horizontal">
            	 	<form id="loginform" >
	                  	<div class="input-group form-group">
	                  		<div class="input-icon">
	                  				<i class="fa fa-user"></i>
	                      			<input type="text" id ="username" name="username" class="form-control" style="width:240px;height:40px;" placeholder="用户名">
	                  		</div>
	                    </div>
	                  	<div class="input-group form-group padding-xs">
	                  		<div class="input-icon">
	                  				<i class="fa fa-lock"></i>
	                  		 		<input type="password" id ="password" name="password" class="form-control" style="width:240px;height:40px;" placeholder="密码">
	                  		</div>
	                    </div>
	                  	<div class="input-group form-group padding-xs">
	                  		<div class="input-icon">
	                  				<i class="fa fa-commenting-o"></i>
	                  				<input type="text" name="authCode" class="form-control" style="width:120px;height:40px;float:left;" placeholder="验证码" /><img title="点击更换验证码" src="${webpath}/auth/getKaptcha" onclick="changeCode()"  id="codePic">
	                  		</div>
                        </div>                        
                    </form>
	                    <div class="form-group">
	                    	<button type="button" id="loginbutton" style="width:240px; margin-right:5px;font-size:16px;background:#1691B4; color:#99FFFF;" class="btn">登录系统</button>
	                	</div>
	                	<div class="form-group">
                    		<a href="#" data-toggle="modal"  data-remote="${webpath}/sysUser/showSysHelpDoc" data-target="#showSysHelpDoc"   style="color:#FFF; font-size:20px; text-shadow:2px 2px 5px #000; padding:5px 0 0 10px;"><i class="fa fa-question-circle"></i> 使用帮助</a>
                            <a href="#" style="color:#FFF;font-size:20px; text-shadow:2px 2px 5px #000; padding:5px 0 0 10px;" id="folder" data-container="body" data-toggle="popover" data-placement="bottom" data-html="true" 
                            data-content="<div id='appQrCode' style=' width:200px; height:200px;'><div>">
                            <i class="fa fa-qrcode"></i> APP下载
                            </a>
                	 	</div>
                </div>
               		  <div style="display:none" >
               			 <%--<iframe src="${snsServer }/?/account/login/" frameborder="0" style="width:100%; height:800px; min-height:auto;"></iframe>                              --%>
               			 <form id="login_form"   method="post" action="${snsServer }/?/account/ajax/fjxm_login_process/" >
							<input type="text" id="aw-login-user-name" class="form-control" name="user_name"/>
							<input type="password" id="aw-login-user-password" class="form-control" name="password" />
               				<input type="text" id ="url" name="url" value="${fjServer }/auth/goHome">
               				<button id="submitBtn" type="submit"></button>
               			 </form>
        </div>
    </div>
    </div>
    <div style="position:absolute; bottom:0px; width:100%; text-align:center;">
    <p style="line-height:0.3em; color:#666; font-size:16px; font-weight:bold;">福建省生态环境厅
    <span style="margin:0 10px;font-size:14px;font-weight:normal;">最近更新日期：${CURRENT_VERSION_DATE}</span><span><a href="#" data-toggle="modal" data-remote="${webpath}/sysUser/toUpdateRecordPage" data-target="#updateRecord"  style="color:#23b7e5;font-size:14px;font-weight:normal;"><i class="fa fa-pencil"></i>更新记录</a></span>
    </p>
   <!-- <p style="line-height:0.1em; color:#999; font-size:14px;">北京长能环境大数据科技有限公司 研发并提供技术支持</p>
    <p style="line-height:0.1em; color:#999; font-size:14px;">服务热线：010-88067626/880672627</p>-->
    <p style="line-height:0.1em; color:#999; font-size:14px;">推荐谷歌及IE11浏览器</p>
    </div>
    <div class="modal fade" id="showSysHelpDoc" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			
			</div>
		</div>
	</div>
	
	<div class="modal fade" id="updateRecord" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			
			</div>
		</div>
	</div>
  	</body>
	<script src="${webpath }/static/js/jsencrypt.min.js"></script>
  	<script type="text/javascript">
        function test(value) {
            if (value != null && value != '') {
                var publicKey = '${publicKey}';
                var encrypt = new JSEncrypt();
                encrypt.setPublicKey(publicKey);
                var encryptPwd = encrypt.encrypt(value);
                return encryptPwd;
            }
            return "";
        }
  	  var WEBPATH='${webpath}';
  	  var snsServer = '${snsServer}';
  	  var fjServer ='${fjServer}';
  	  //刷新验证码
  	  var changeCode=function(){
  		 $("#codePic").prop("src",WEBPATH+"/auth/getKaptcha?flag="+Math.random());  
  	  }
      $(document).ready(function(){
    	  
    	business.listenEnter("loginbutton");
    	//表单非空验证
        $("#loginform").formValidation({
	        framework: 'bootstrap',
	        message: 'This value is not valid',
	        icon:{
		            valid: 'glyphicon glyphicon-ok',
		            invalid: 'glyphicon glyphicon-remove',
		            validating: 'glyphicon glyphicon-refresh'
                   },
	        fields: {
	        	username: {
	                message: '用户名不允许为空',
	                validators: {
	                    notEmpty: {
	                        message: '用户名为必填项'
	                    },
	                    stringLength: {
	                        min: 5,
	                        max: 30,
	                        message: '用户名为5-30个字符'
	                    },
	                    regexp: {
	                        /* regexp: /^[a-zA-Z0-9|\-]+$/,
	                        message: '用户名由字母、数字中划线组成' */
	                    	regexp: /^[a-zA-Z0-9_\.\-]+$/,
	                        message: '用户名由字母、数字、中划线、下划线和点组成'
	                    }
	                
	            }
	            
	          },
	           password: {
	                validators: {
	                    notEmpty: {
	                        message: '密码不能为空'
	                    },
	                    stringLength: {
	                        min: 8,
	                        max: 16,
	                        message: '密码为8-16个字符'
	                    },
	                    regexp: {
	                        //regexp: /^[a-zA-Z0-9]+$/,
	                        //message: "密码由字母、数字组成"
	                    //	regexp:   /^\S+$/,
                           // message:"不允许空格"
                           	regexp: /(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,50}/,
				 message: "密码由字母、数字和特殊字符组成"
	                    }
	                }
	            },
	            authCode: {
	                validators: {
	                	 notEmpty: {
		                        message: '验证码不能为空'
		                 },
		                 stringLength: {
		                        min: 4,
		                        max: 4,
		                        message: '验证码为4个字符'
		                 },
	                     //regexp: {
	                     //   regexp: /^[a-zA-Z0-9]+$/,
	                     //   message: "密码由字母、数字组成"
	                    // }
	                }
	            }
	          }       
	    });
    	
        $(".form-control-feedback").attr("style","left:240px;");  
    	
        function loding(btn){
			document.getElementById(btn).innerHTML = "登录中..."
			document.getElementById(btn).disabled = "disabled"
		}
        //表单提交
        $('#loginbutton').click(function() {
            document.getElementById('password').value = test(document.getElementById('password').value);
        				 $("#loginform").data('formValidation').validate();
			             var validate = $("#loginform").data('formValidation').isValid();
			             if(validate){
			            	 loding('loginbutton');
			             	var options = {
					             url:'/ajaxLogin',
					             type: 'post',
					             async:false,
					             success:function(data){
					            		 if (data.meta.result == 'success') {
										 window.location.href = WEBPATH + "/auth/goHome";
											//  window.location.href = WEBPATH + "/sysIndex/indexHome?showLoginAlert=1";

										 } else {
										 changeCode();
										 swal("登录失败!", "用户名或密码错误", "error");
                                             document.getElementById('password').value ='';
                                                 document.getElementById("loginbutton").innerHTML = "登录系统";
										 document.getElementById("loginbutton").removeAttribute("disabled");
									 }

							// if(data.meta.result=='success'){
					            	//	$.ajax({
							//					type : "get",
							//					dataType:'jsonp',
							//					async:false,
							//					url :snsServer+'/?/account/ajax/app_logout/',
							//					error : function(request) {
							//					},
							//					success : function(item) {
							//					}
							//			 })
					            	//	 var username = $("#username").val();
					            	//	 var password =$("#password").val();
					                 //调用社区 接口，重定向到福建项目中 
					                 //	//var isSns = data.data.is_sns;
					                 //	//if(isSns =='1'){
					                 //		//注册过，直接登录社区
					                 //		 $("#aw-login-user-name").val(username);
					                 //		 $("#aw-login-user-password").val(password);
					                 //		 $("#submitBtn").trigger("click");
					                 //	//}
					            	 //}else{
					            	//	 changeCode();
					            	//	 swal("登录失败!",data.meta.detailMessage, "error");
					            	//	 document.getElementById("loginbutton").innerHTML = "登录系统";
					       		//    	 document.getElementById("loginbutton").removeAttribute("disabled");
					            	// }
					            }
							,
					            error:function (data) {
				if(data.status==500){
				 swal("登录失败!","密码不正确", "error");
				}             
				    
                                    changeCode();
                                   // swal("登录失败!", data.meta.detailMessage, "error");
                                    document.getElementById("loginbutton").innerHTML = "登录系统";
                                    document.getElementById("loginbutton").removeAttribute("disabled");
									
                                }
					         };
					         $('#loginform').ajaxSubmit(options);
					       	 return true;
			             }else{
						        //表单未填写
						        $("#loginform").data('formValidation').validate();
						        return false;
	       
	                     } 
	           
	    }); 
	       
	});
</script>
<script>
	$(function () { 
		$("[data-toggle='popover']").popover();
	});
	
    $('body').on('click', function(event) {  
		var target = $(event.target); // One jQuery object instead of 3  
		// Compare length with an integer rather than with   
		if (!target.hasClass('popover')  
				&& target.parent('.popover-content').length === 0  
				&& target.parent('.myPopover').length === 0  
				&& target.parent('.popover-title').length === 0  
				&& target.parent('.popover').length === 0 && target.attr("id") !== "folder") {  
				$('#folder').popover('hide');  
		}  

		$('#appQrCode').empty();
		$('#appQrCode').qrcode({
			render : 'canvas',
			text : "${downloadAppUrl}",
			height : 200,
			width : 200,
		});
		/* //从 canvas 提取图片 image 
		function convertCanvasToImage(canvas) {
			//新Image对象，可以理解为DOM 
			var image = new Image();
			// canvas.toDataURL 返回的是一串Base64编码的URL，当然,浏览器自己肯定支持 
			// 指定格式 PNG 
			image.src = canvas.toDataURL("image/png");
			return image;
		}

		$('#centerImg').empty();
		//获取网页中的canvas对象 
		var mycanvas1 = document.getElementsByTagName('canvas')[0];
		//将转换后的img标签插入到html中 
		var img = convertCanvasToImage(mycanvas1);
		$('#centerImg').html(img);//imagQrDiv表示你要插入的容器id
        
		var margin = ($("#centerImg").height() - $("#centerCodeIco").height())
		// 2; / / 控制Logo图标的位置
		$("#centerCodeIco").css("margin", margin); */
	}); 
    </script>
</html>
