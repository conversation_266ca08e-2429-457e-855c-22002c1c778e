 <%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<html lang="en">
<head>
<link rel="shortcut icon" href="${webpath }/static/img/zhn_favicon.ico" />
<!-- Bootstrap core CSS -->
<link rel="stylesheet" type="text/css" href="${webpath }/static/libs/bootstrap/3.3.4/css/bootstrap.css">
<%-- <link rel="stylesheet" type="text/css" href="${webpath }/static/css/bootstrap.min.css"> --%>
<!--<link rel="stylesheet" type="text/css" href="${webpath }/static/jquery/bootstrap-3.3.4.css">-->
<!-- Font Awesome -->
<link rel="stylesheet" type="text/css" href="${webpath }/static/font-awesome/4.7.0/css/font-awesome.min.css">
<!-- Morris -->
<link href="${webpath }/static/css/morris.css" rel="stylesheet" />
<!-- bootstrapValidator -->
<link href="${webpath }/static/css/formValidation.min.css" rel="stylesheet" type="text/css"/>
<!-- Animate -->
<link href="${webpath }/static/css/animate.min.css" rel="stylesheet">
<link href="${webpath }/static/css/bootstrap-table.css" rel="stylesheet">
<!-- Owl Carousel -->
<link href="${webpath }/static/css/owl.carousel.min.css" rel="stylesheet">
<link href="${webpath }/static/css/owl.theme.default.min.css" rel="stylesheet">
<!-- sweetalert  -->
<link href="${webpath }/static/css/sweetalert.css" rel="stylesheet">
<!-- Simplify -->
<link href="${webpath }/static/css/simplify.min.css" rel="stylesheet">
<!-- 上传附件 CSS -->
<link href="${webpath }/static/bootstrap-fileinput-4.3.9/css/fileinput.css" rel="stylesheet" type="text/css" />
<link href="${webpath }/static/bootstrap-fileinput-4.3.9/themes/explorer/theme.css" rel="stylesheet" type="text/css" />

<link href="${webpath }/static/css/style.css" rel="stylesheet">
<!-- 遮罩层 -->
<link href="${webpath }/static/css/loding.css" rel="stylesheet">

<!-- bootstrap-datetimepicker.css -->
<link href="${webpath }/static/css/bootstrap-datetimepicker.css" rel="stylesheet" />
<!--select2-bootstrap.css  -->
<link href="${webpath }/static/css/select2-bootstrap.css" rel="stylesheet" />

<!-- Custom CSS -->
<link href="${webpath }/static/dist/css/sb-admin-2.css" rel="stylesheet">

<!-- bootstraptable固定行和列 -->
<link href="${webpath }/static/css/bootstrap-table-fixed-columns.css" rel="stylesheet">

<!-- 时间轴 CSS -->
<link href="${webpath }/static/css/time/css.css" rel="stylesheet" type="text/css" />


 <script src="${webpath }/static/jquery/2.1.1/jquery.min.js"></script> 
<script src="${webpath }/static/js/vue.min.js"></script>
<script type="text/javascript" src="${webpath }/static/js/formValidation.min.js"></script>
<script src="${webpath }/static/js/bootstrap-table.js"></script>
<script src="${webpath }/static/js/bootstrap-table-zh-CN.js"></script>
<script type="text/javascript" src="${webpath }/static/businessJs/sysHome/sysMain.js"></script>
<script type="text/javascript" src ='${webpath }/static/js/qrcode.min.js'></script>	
<script type="text/javascript" src ='${webpath }/static/js/jquery.qrcode.min.js'></script>	
<script type="text/javascript" src ='${webpath }/static/js/utf16toutf8.js'></script>	
<title>污染源精细化监管系统</title>
<script type="text/javascript">
	var WEBPATH = '${webpath}';
</script>
<script type="text/javascript">
		$(document).ready(function(){
			//设置查询enter键无效
			business.listenEnter('gpcSearch');
			$('#dataTable').bootstrapTable({       
				 method: 'post',
				 dataType: "json", 
				 url:"https://std.12369.com/GPCStandard/fjzf/getStandardMainListForFjzf.do",
			     undefinedText : '-',  
			     pagination : true, // 分页  
			     striped : true, // 是否显示行间隔色  
			     cache : false, // 是否使用缓存  
			     pageSize:15, // 设置默认分页为 20
			     pageNumber: 1,
			     queryParamsType: "",
			     locale:'zh-CN',
			     pageList: [5,10, 20, 30,50], // 自定义分页列表
			     singleSelect: true,
			     contentType: "application/x-www-form-urlencoded;charset=UTF-8",
			     sidePagination: "server", //服务端请求
			     icons:{
			    	  paginationSwitchDown: 'glyphicon-collapse-down icon-chevron-down',
			    	  paginationSwitchUp: 'glyphicon-collapse-up icon-chevron-up',
			    	  refresh: 'glyphicon-refresh icon-refresh',
			    	  toggle: 'glyphicon-list-alt icon-list-alt',
			    	  columns: 'glyphicon-th icon-th',
			    	  detailOpen: 'glyphicon-plus icon-plus',
			    	  detailClose: 'glyphicon-minus icon-minus'
			     },
			     queryParams:function (params) {
			            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
			                 page: params.pageNumber,
			                 rows: params.pageSize,
			                 sort:"standardCode",
			                 order:"asc",
			               	 keyword:$("#keyword").val(), //关键词
			               	 standardLevel:$("#standardLevel").val(),//标准级别
			               	 mediumAttribute:$("#mediumAttribute").val(), //介质属性
			               	 applicableRegion:$("#applicableRegion").val()//适用地区
			            };
			            return temp;
			     },//参数
			     uniqueId : "id", // 每一行的唯一标识  
				 columns: [
				           {
					           field: "standardName",
					           title: "标准名称",
					           formatter: function(value,row,index){
						        	  return "<a href ='javascript:void(0)' style ='color:#6CCCF1;' onclick=detailInfoPage('"+row.id+"')>"
										+value+"</a>";
						       }
					       },
				           {
					           field: "applicableRegion",
					           title: "适用地区",
					           align: 'center'
				           },
				           {
					           field: "standardCode",
					           title: "标准号",
					           align: 'center'
				           },
				           {
					           field: "availability",
					           title: "标准有效性",
					           align: 'center'
				           }
				 ],
				 responseHandler : function(res) {  
		               return {  
		                   total : res.total,  
		                   rows : res.rows  
		               };  
		         },
	             onCheck: function(row, $element) {
	        	   
	             },//单击row事件
		         onUncheck: function(row, $element) {
		        		
			     },
			     onUncheckAll: function(row, $element) {
			       			
			     },
			     onCheckAll:function(row, $element) {
			        		
			     },
			     onRefresh: function () {
			        		
			     },
		         formatLoadingMessage: function () {
		        	   return "请稍等，正在加载中...";
		         },
		         formatNoMatches: function () { //没有匹配的结果
		        		   return '无符合条件的记录';
		         }
			});
			
			
			//获取省份信息异步查询
			$.ajax({
				type:"post",
				url:"https://std.12369.com/GPCStandard/fjzf/getProviceDataForFjzf.do",
				dataType:"json",
				success:function(data){
			       new Vue({
			  		  el: '#provinceDiv',
					  data: {
						  provinceObj:data
					  }
				   });
				}
		    });
			
			//介质属性
			$.ajax({
				type:"post",
				url:"https://std.12369.com/GPCStandard/fjzf/getMediumAttributeListForFjzf.do",
				dataType:"json",
				success:function(data){
			       new Vue({
			  		  el: '#mediumAttributeDiv',
					  data: {
						  mediumAttributeDivObj:data
					  }
				   });
				}
		    });
			
			//绑定搜索按钮
			$('#gpcSearch').click(function() {
				$('#dataTable').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:15});
		   	});
       		
	    });
		
		function detailInfoPage(id){
		   	window.self.location.href=WEBPATH + '/GPCStandard/getStandardMainByIdForPage?id='+id;
		}
</script>
</head>
<body class="overflow-hidden">
				<div class="padding-md">
                	<div class="row">  
                    <div class="col-lg-12"> 
                            <div class="smart-widget widget-blue">
                                <div class="smart-widget-header font-16">
                                    <i class="fa fa-arrow-right"></i> 环境标准
                                </div>
                              <div class="smart-widget-inner table-responsive">
                                <div class="smart-widget-hidden-section">
                                        <ul class="widget-color-list clearfix">
                                            <li style="background-color:#20232b;" data-color="widget-dark"></li>
                                            <li style="background-color:#4c5f70;" data-color="widget-dark-blue"></li>
                                            <li style="background-color:#23b7e5;" data-color="widget-blue"></li>
                                            <li style="background-color:#2baab1;" data-color="widget-green"></li>
                                            <li style="background-color:#edbc6c;" data-color="widget-yellow"></li>
                                            <li style="background-color:#fbc852;" data-color="widget-orange"></li>
                                            <li style="background-color:#e36159;" data-color="widget-red"></li>
                                            <li style="background-color:#7266ba;" data-color="widget-purple"></li>
                                            <li style="background-color:#f5f5f5;" data-color="widget-light-grey"></li>
                                            <li style="background-color:#fff;" data-color="reset"></li>
                                        </ul>
                                  </div>
                                  <div class="smart-widget-body form-horizontal">
                                      <div class="form-group">
                                            <label class="control-label col-md-2">关键字</label>
                                            <div class="col-md-3">
                                                <input type="text" id="keyword" placeholder="请输入要查询的关键字" class="form-control" data-parsley-required="true">
                                            </div>
                                            <label class="control-label col-md-2">介质属性</label>
                                            <div id="mediumAttributeDiv" class="col-md-3">
                                                <select id="mediumAttribute" class="form-control">
                                                    <option value="">——请选择——</option>
                                                    <option v-for="(obj, index) in mediumAttributeDivObj" :value='obj.name'>{{obj.name}}</option>
                                                </select>
                                            </div>
										</div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">标准级别</label>
                                            <div class="col-md-3">
                                                <select id="standardLevel" class="form-control">
                                                    <option value="">——请选择——</option>
                                                      <option value="国家标准">国家标准</option>
                                                      <option value="地方标准">地方标准</option>
                                                      <option value="行业标准">行业标准</option>
                                                </select>
                                            </div>
                                            <label class="control-label col-md-2">适用地区</label>
                                            <div id="provinceDiv" class="col-md-3">
                                                <select id="applicableRegion" class="form-control">
                                                      <option value="">——请选择——</option>
                                                      <option  v-for="(obj, index) in provinceObj" :value='obj.name'>{{obj.name}}</option>
                                                </select>
                                            </div>
                                            <button class="btn btn-info" type="button" id="gpcSearch" style="width:120px;">查询</button>
										</div>
                                  </div>
                                </div>
                            </div>
                        </div>
                    </div>
					<!--环保智库row-->
                    <div class="row">                        
                        <div class="col-md-12"> 
                            <div class="smart-widget widget-blue">
                                <div class="smart-widget-header font-16">
                                    <i class="fa fa-arrow-right"></i> 环境标准 - 搜索结果
                                </div>
                              <!-- <div class="smart-widget-inner table-responsive">
                                <div class="smart-widget-hidden-section">
                                        <ul class="widget-color-list clearfix">
                                            <li style="background-color:#20232b;" data-color="widget-dark"></li>
                                            <li style="background-color:#4c5f70;" data-color="widget-dark-blue"></li>
                                            <li style="background-color:#23b7e5;" data-color="widget-blue"></li>
                                            <li style="background-color:#2baab1;" data-color="widget-green"></li>
                                            <li style="background-color:#edbc6c;" data-color="widget-yellow"></li>
                                            <li style="background-color:#fbc852;" data-color="widget-orange"></li>
                                            <li style="background-color:#e36159;" data-color="widget-red"></li>
                                            <li style="background-color:#7266ba;" data-color="widget-purple"></li>
                                            <li style="background-color:#f5f5f5;" data-color="widget-light-grey"></li>
                                            <li style="background-color:#fff;" data-color="reset"></li>
                                        </ul>
                                  </div> -->
                                  <div class="form-horizontal">
                                     <table class="table-no-bordered" id="dataTable">
										</table>
                                  </div>
                                </div>
                            </div>
                   </div>
            </div>
<div id="mask">
				  <div class="spinner">
				  <div class="spinner-container container1">
				    <div class="circle1"></div>
				    <div class="circle2"></div>
				    <div class="circle3"></div>
				    <div class="circle4"></div>
				  </div>
				  <div class="spinner-container container2">
				    <div class="circle1"></div>
				    <div class="circle2"></div>
				    <div class="circle3"></div>
				    <div class="circle4"></div>
				  </div>
				  <div class="spinner-container container3">
				    <div class="circle1"></div>
				    <div class="circle2"></div>
				    <div class="circle3"></div>
				    <div class="circle4"></div>
				  </div>
				</div>
				</div>
</body>
<script type="text/javascript">
	$(document).ready(function(){
		//监听enter查询，内包含监听回退键
		business.listenEnter("seachBtn");
	});
</script>
<script type="text/javascript" src="${webpath }/static/js/jquery.form.js"></script>
	<script type="text/javascript" src="${webpath }/static/libs/bootstrap/3.3.4/js/bootstrap.min.js"></script>
	<script type="text/javascript" src="${webpath }/static/js/bootstrap.min.js"></script>
	
	<!-- Slimscroll -->
	<script src='${webpath }/static/js/jquery.slimscroll.min.js'></script>
	
	<%-- <script type="text/javascript" src="${webpath}/static/easyui/jquery.easyui.min.js"></script> --%>
	<!-- Morris -->
	<script src='${webpath }/static/js/rapheal.min.js'></script>
	<script src='${webpath }/static/js/morris.min.js'></script>
	<!-- Sparkline -->
	<script src='${webpath }/static/js/sparkline.min.js'></script>
	<!-- Skycons -->
	<script src='${webpath }/static/js/uncompressed/skycons.js'></script>
	<!-- Popup Overlay -->
	<script src='${webpath }/static/js/jquery.popupoverlay.min.js'></script>
	<!-- Easy Pie Chart -->
	<script src='${webpath }/static/js/jquery.easypiechart.min.js'></script>
	<!-- Sortable -->
	<script src='${webpath }/static/js/uncompressed/jquery.sortable.js'></script>
	<!-- Owl Carousel -->
	<script src='${webpath }/static/js/owl.carousel.min.js'></script>
	<!-- Modernizr -->
	<script src='${webpath }/static/js/modernizr.min.js'></script>
	<!-- Simplify -->
	<script src="${webpath }/static/js/simplify/simplify.js"></script>
	
	<!--  注释一下两个js，是因为该js中报错，影响子页面嵌套中的$(function(){})初始化方法的加载，若需要这两个js，直接在子页面上在放在最下面引用，  最终解释权：池哥 -->
  <%--<script src="${webpath }/static/js/simplify/simplify_dashboard.js"></script>
		<!-- Flot -->
	<script src='${webpath }/static/js/jquery.flot.min.js'></script> --%>
	
	
	<!-- sweetalert -->
	<script src="${webpath }/static/js/sweetalert.min.js"></script>
	<!-- bootstrap-datetimepicker -->
	<script src='${webpath }/static/js/bootstrap-datetimepicker.js'></script>
	<script src='${webpath }/static/js/bootstrap-paginator.js'></script>
	<%-- <script type="text/javascript" src="${webpath}/static/easyui/easyui-lang-zh_CN.js"></script> --%>
	<!--任务要求附件上传-->
	<script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/fileinput.js" type="text/javascript"></script>
		<script src="${webpath }/static/bootstrap-fileinput-4.3.9/themes/explorer/theme.js" type="text/javascript"></script>
	
	
	<%--<script src='${webpath }/static/js/iview.js'></script> --%>
	
    <!-- 业务逻辑相关js -->
	<script type="text/javascript" src="${webpath}/static/common/js/loding.js"></script>
	<script type="text/javascript" src="${webpath}/static/common/js/macro.js"></script>
	<script type="text/javascript" src="${webpath}/static/common/js/business.js"></script>
	<script type="text/javascript" src="${webpath}/static/common/js/message.js"></script>
	<script type="text/javascript" src="${webpath}/static/common/js/phone.js"></script>
	<script type="text/javascript" src="${webpath }/static/easyui/jquery.easyui.min.js"></script>
	<script type="text/javascript" src="${webpath }/static/easyui/easyui-lang-zh_CN.js"></script>
	<!-- zTree -->
	<link rel="stylesheet" href="${webpath }/static/zTree_v3-master/css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="${webpath }/static/zTree_v3-master/js/jquery.ztree.core.js"></script>
	<script type="text/javascript" src="${webpath }/static/zTree_v3-master/js/jquery.ztree.excheck.js"></script>
	<!-- pdfobject -->
	<%-- <script src="${webpath }/static/pdfjs/web/jQuery.XDomainRequest.js"></script> --%>
	<script type="text/javascript" src="${webpath }/static/PDFObject/pdfobject.min.js"></script> 
    <!-- 禁止使用F5刷新功能 -->
    <!--<script type="text/javascript" src="${webpath }/static/js/nof5.js"></script>-->
    
    <script charset="utf-8" type="text/javascript" src="${webpath }/static/js/echarts-all-3.js"></script>
    <!-- 打开关闭标签js -->
    <script type="text/javascript" src="${webpath }/static/js/openCloseTag.js"></script>
     
    
    <!-- bootstraptable固定行和列 -->
    <script type="text/javascript" src="${webpath }/static/js/bootstrap-table-fixed-columns.js"></script>
</html>