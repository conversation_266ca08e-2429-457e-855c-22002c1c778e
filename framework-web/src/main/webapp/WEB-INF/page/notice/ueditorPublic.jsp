<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<c:set var="webpath">${pageContext.request.contextPath }</c:set>
<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<script type="text/javascript">
		var WEBPATH = '${webpath}';
	</script>
	<title>Table</title>
</head>

<body>
	
	<script type="text/javascript">
		var ue = UE.getEditor('editor', {
			//这里可以选择自己需要的工具按钮名称,此处仅选择如下五个  
			toolbars : [ [ 
				'fullscreen', 'undo', 'redo', '|', 
				'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'removeformat', '|', 
				'forecolor', 'backcolor', 'lineheight', 'cleardoc', '|', 
				'paragraph', 'fontfamily', 'fontsize', '|', 
				'indent', '|', 
				'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 
				'link', 'unlink', '|', 
				'simpleupload', 'insertimage', '|', 
				'horizontal', 'date', 'time', '|', 
				'inserttable', 'deletetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol',
				'mergecells', 'mergeright', 'mergedown', 'splittocells',
				'splittorows', 'splittocols', '|', 
				'print', 'searchreplace'
			] ],
			//字数统计  
			wordCount : true,
			//关闭elementPath  
			elementPathEnabled : false,
			//默认的编辑区域高度  
			initialFrameHeight : 400,
			//是否自动保存
			enableAutoSave: false
			//更多其他参数，请参考ueditor.config.js中的配置项  
		});
		//改变图片上传和读取路径
		UE.Editor.prototype._bkGetActionUrl = UE.Editor.prototype.getActionUrl;
		UE.Editor.prototype.getActionUrl = function(action) {
			//判断路径   这里是config.json 中设置执行上传的action名称
			if (action == 'uploadimage') {
				return WEBPATH + '/notice/uploadImage';
			} else if (action == 'uploadvideo') {
				return '';
			} else {
				return this._bkGetActionUrl.call(this, action);
			}
		}

	</script>

</body>

</html>