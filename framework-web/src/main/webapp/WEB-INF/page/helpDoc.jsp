<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<script type="text/javascript">
$(document).ready(function(){
	$('#dataTable').bootstrapTable({       
		 method: 'post',
		 dataType: "json", 
		 url:  WEBPATH+'/sysUser/showSysHelpDocList',
	     undefinedText : '-',  
	     pagination : true, // 分页  
	     striped : true, // 是否显示行间隔色  
	     cache : false, // 是否使用缓存  
	     pageSize:10, // 设置默认分页为 20
	     pageNumber: 1,
	     queryParamsType: "",
	     locale:'zh-CN',
	     clickToSelect:true,
	     pageList: [5,10, 20, 30,50], // 自定义分页列表
	     singleSelect: false,
	     contentType: "application/x-www-form-urlencoded;charset=UTF-8",
	     sidePagination: "server", //服务端请求
	     icons:{
	    	  paginationSwitchDown: 'glyphicon-collapse-down icon-chevron-down',
	    	  paginationSwitchUp: 'glyphicon-collapse-up icon-chevron-up',
	    	  refresh: 'glyphicon-refresh icon-refresh',
	    	  toggle: 'glyphicon-list-alt icon-list-alt',
	    	  columns: 'glyphicon-th icon-th',
	    	  detailOpen: 'glyphicon-plus icon-plus',
	    	  detailClose: 'glyphicon-minus icon-minus'
	     },
	     queryParams:function (params) {
	            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
         		  pageNum: params.pageNumber,
                  pageSize: params.pageSize,
	            };
	            return temp;
	     },//参数
	     uniqueId : "id", // 每一行的唯一标识  
		 columns: [
				    {
			           title: "序号",
			           align: 'center',
			           width:50,
			           valign:'middle',
			           formatter: function(value,row,index){
				        	return index+1;
				       }
		       	   },
		           {
			           field: "fileName",
			           title: "文件",
			           align: 'center',
			           valign:'middle'
			       },
		           {
			           field: "updateDate",
			           title: "更新时间",
			           align: 'center',
			           valign:'middle',
			           width:100,
			   		   formatter : function(value){
							if(value==null || value==''){return '';}
			                var date = new Date(value);
			                var y = date.getFullYear();
			                var m = date.getMonth() + 1;
			                var d = date.getDate();
			                return y + '-' +m + '-' + d;
			            }
		           },
		           {
			           field: "fileSize",
			           title: "大小",
			           align: 'center',
			           valign:'middle',
			           width:100,
			           formatter: function(value,row,index){
			        	   return bytesToSize(value);
				       }
		           },
		           {
			           field: "downloadTimes",
			           title: "下载次数",
			           align: 'center',
			           valign:'middle',
			           width:100
		           },
		           {    
		        	   field: 'id',
			           title: "操作",
			           align: 'center',
			           valign:'middle',
			           width:100,
			           formatter: function(value,row,index){
			        	  return "<a href=\"#\" onclick=\"downloadSysHelpDoc('"+value+"')\"><i class=\"fa fa-download\" style=\"color:#23b7e5;\"></i></a>";;
			           }
		           }
		 ],
		 responseHandler : function(res) {  
               return {  
                   total : res.total,  
                   rows : res.list  
               };  
         },
         onCheck: function(row, $element) {
    	   
         },//单击row事件
         onUncheck: function(row, $element) {
        		
	     },
	     onUncheckAll: function(row, $element) {
	       			
	     },
	     onCheckAll:function(row, $element) {
	        		
	     },
	     onRefresh: function () {
	        		
	     },
         formatLoadingMessage: function () {
        	   return "请稍等，正在加载中...";
         },
         formatNoMatches: function () { //没有匹配的结果
        		   return '无符合条件的记录';
         }
	});
	
	$('#showSysHelpDoc').on('hide.bs.modal', function () {
		   $(this).removeData("bs.modal");  
	})
})		
function downloadSysHelpDoc(id){
	window.location.href= WEBPATH+'/sysUser/downLoadSysHelpDoc?id='+id;
}
function bytesToSize(bytes) {
    if (bytes === 0) return '0 B';
    var k = 1024, // or 1024
        sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
        i = Math.floor(Math.log(bytes) / Math.log(k));
   return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
}
</script>
</head>
<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal"
			aria-hidden="true">&times;</button>
		<h4 class="modal-title" id="myModalLabel">使用帮助</h4>
	</div>
	<div class="modal-body">
		<table id="dataTable" class="table table-striped table-hover table-no-bordered">
			
		</table>
	</div>
	<div class="modal-footer" style="margin-top: 40px;">
		<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
	</div>
