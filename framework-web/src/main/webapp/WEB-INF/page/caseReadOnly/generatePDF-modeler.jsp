<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<html lang="en">
  	<head>
	    <title>环境监察全过程业务智能办理系统</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
	    <meta name="description" content="">
	    <meta name="author" content="">
		<script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"  type="text/javascript"></script>
</head>
<body>
<script type="text/javascript">
	$(function(){
		//监听回退键
		business.listenBackSpace();    
	});
</script>
<script type="text/javascript">

//初始化vue信息，绑定动态项 
$(document).ready(function() {
	var vueFormData={tablesName:'${generateBean.tablesName}',upOrDowm:'${generateBean.upOrDowm}',objectId:'${generateBean.objectId}',baseId:'${generateBean.baseId}'};
	// 请求数据 
	$.ajax({
        cache: true,
        type: "POST",
        url: WEBPATH+'/atvEasy/generate-content-vue',
        data:vueFormData, 
        async: false,
        error: function(request) {
        	swal("错误!","网络异常", "error");
			business.closewait();
        },
        success: function(data) {
        	generateBaseArr = data; // 绑定 VUE （json）
        }
    });
	generateVue = new Vue({ // 不要用var 声明，定义为全局 
		  el: '#generateTable',
		  data: {
		    items:generateBaseArr,
		  },
		  methods: {
			  changeContent:  function (contentId,itemRemark,index) { // 编辑
				  changeGenerate(contentId,itemRemark,index);
			  }, 
			  deleteContent: function (contentId,index){ //删除 
				  deleteGenerate(contentId, index);
			  },
			  chevronDownOrUp:function (contentIdA,contentIdB,index,type){ // 上移  下移
				  chevronGenerateDownOrUp(contentIdA,contentIdB,index,type);
			  },
			  attachShow: function(contentId){
				  showAttach(contentId);
			  }
		  }
	});
});

function showAttach(itemId){
	var tableName = '${generateBean.tablesName}';
	var options = {
			remote:WEBPATH+'/attachment/onekeyShowAttach?tableName='+tableName+'&itemId='+itemId
		  };
	$('#ckfj').modal(options);
}

function changeGenerate(contentId,itemRemark,index){
	// 先清空 
	$("#beizhu-contentId").val("");
	$("#beizhu-itemRemark").val("");
	$("#beizhu-index").val("");
	// 在赋值
	$("#beizhu-contentId").val(contentId);
	$("#beizhu-itemRemark").val(itemRemark);
	$("#beizhu-index").val(index);
}
function saveItemRemark(){
	var itemText =  $("#beizhu-itemRemark").val(); 
	if(itemText.length>1000){
		swal("错误!", "备注最大5000字符!", "error");
		return;
	}
	var itemIndex = $("#beizhu-index").val();
	//  vue 追加 var obj ={id:data.code,itemRemark:data.message}; generateBaseArr.push(obj);
	// 拉起遮罩
	business.openwait();
	$.ajax({
		cache : true,
		type : "POST",
		url : WEBPATH + '/atvEasy/update-itemremark',
		data : $('#generateContentForm').serialize(), 
		async : false,
		error : function(request) {
			business.closewait();
			swal("错误!","网络异常", "error");
		},
		success : function(data) {
			business.closewait();
			if (data.result == 'success') {// 成功
				swal({title : "成功", text : "", type : "success" }, function() {
						generateVue.items[itemIndex].itemRemark =data.message;
				})
			} else if (data.result == 'error') { // 失败  
				swal({ title : "失败", text : "备注信息格式不正确", type : "error" });
			} else {
				swal({ title : "返回信息错误", text : "", type : "error" });
			}
		}
	});
	$('#beizhu').modal('hide')
}
function deleteGenerate(contentId,index){
	swal({
		title : "您确定要删除这条信息吗",
		text : "删除后将无法恢复，请谨慎操作！",
		type : "warning",
		showCancelButton : true,
		confirmButtonColor : "#DD6B55",
		confirmButtonText : "是的，我要删除！",
		cancelButtonText : "让我再考虑一下",
		closeOnConfirm : false,
		closeOnCancel : false
	}, function(isConfirm) {
		if (isConfirm) {
			business.openwait();
			var obj={tablesName:'${generateBean.tablesName}',contentIdA:contentId};
			$.ajax({
				cache : true,
				type : "POST",
				url : WEBPATH + '/atvEasy/generate-delete',
				data : obj, 
				async : false,
				error : function(request) {
					swal("错误!","网络异常", "error");
					business.closewait();
				},
				success : function(data) {
					business.closewait();
					if (data.result == 'success') {// 成功
						swal({ title : "删除成功！", text : "您已经永久删除了这条信息。", type : "success" }, function() {
							generateBaseArr.splice(index,1);
						 })
					} else if (data.result == 'error') { // 失败  
						swal({ title : "删除失败", text : "", type : "error" });
					} else {
						swal({ title : "返回信息错误", text : "", type : "error" });
					}
				}
			});
		} else {
			swal({
				title : "已取消",
				text : "您取消了删除操作！",
				type : "error"
			})
		}
	})
}
function chevronGenerateDownOrUp(contentIdA,contentIdB,index,type){
	var obj={tablesName:'${generateBean.tablesName}',contentIdA:contentIdA,contentIdB:contentIdB,index:index,type:type};
	if(type==0){// 上移
		var vueDown  = generateVue.items[index];
		var vueTop = generateVue.items[index-1];
		// 拉起遮罩
		business.openwait();
		//  type 0：上移 1 ：下移
		$.ajax({
			cache : true,
			type : "POST",
			url : WEBPATH + '/atvEasy/generate-save-loction',
			data :obj, 
			async : false,
			error : function(request) {
				business.closewait();
				swal("错误!","网络异常", "error");
			},
			success : function(data) {
				business.closewait();
				if (data.result == 'success') {// 成功
					Vue.set(generateVue.items, index, vueTop);
					Vue.set(generateVue.items, index-1, vueDown);
					var trId = $("#contentTr"+(index-1));
					trId.fadeOut("fast");
					setTimeout(function(){ trId.fadeIn("fast"); },100)
				} else if (data.result == 'error') { // 失败  
					swal({ title : "移动失败", text : "", type : "error" });
				} else {
					swal({ title : "返回信息错误", text : "", type : "error" });
				}
			}
		  });
	}
	if(type==1){// 下移 
		var vueTop  = generateVue.items[index];
		var vueDown = generateVue.items[index+1];
		// 拉起遮罩
		business.openwait();
		$.ajax({
			cache : true,
			type : "POST",
			url : WEBPATH + '/atvEasy/generate-save-loction',
			data :obj, 
			async : false,
			error : function(request) {
				business.closewait();
				swal("错误!","网络异常", "error");
			},
			success : function(data) {
				business.closewait();
				if (data.result == 'success') {// 成功
					Vue.set(generateVue.items, index, vueDown);
					Vue.set(generateVue.items, index+1, vueTop);
					var trId = $("#contentTr"+(index+1));
					trId.fadeOut("fast");
					setTimeout(function(){ trId.fadeIn("fast"); },100)
				} else if (data.result == 'error') { // 失败  
					swal({ title : "移动失败", text : "", type : "error" });
				} else {
					swal({ title : "返回信息错误", text : "", type : "error" });
				}
			}
		});
	}
	
}

</script>

<div class="main-container">
			<div class="padding-md">
            	<div class="row">	
                	<div class="col-lg-12">
						<div class="smart-widget widget-blue">
                        	<div class="smart-widget-inner table-responsive">
                                <div class="padding-xs" style="float:right;">
                                    <button type="button" class="btn btn-info btn-block" onClick="javascript:history.go(-1);">返回</button>
                                </div>
                                <div class="padding-xs" style="float:right;">
                                    <button type="button" class="btn btn-info btn-block" data-toggle="modal" data-target="#ajyl">案卷预览</button>
                                </div>
                                <div class="padding-xs" style="float:right;">
                                    <button type="button" class="btn btn-info btn-block" onclick="addGenerateItem()">添加条目</button>
                                </div>
                            	<div class="padding-xs" style="float:right;">
                                    <button type="button" class="btn btn-info btn-block"  onclick="saveGenerateTypeName()">保存</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>		
				<div class="row">
					<div class="col-lg-12">
						<div class="smart-widget widget-blue">
							<div class="smart-widget-header font-16">
								<i class="fa fa-arrow-right" style="font-size: 16px;"></i> 一键生成案卷 <span
									class="smart-widget-option"> <span
									class="refresh-icon-animated"> <i
										class="fa fa-circle-o-notch fa-spin"></i>
								</span> <a href="#" class="widget-toggle-hidden-option"> <i
										class="fa fa-cog"></i>
								</a> <a href="#" class="widget-collapse-option"
									data-toggle="collapse"> <i class="fa fa-chevron-up"></i>
								</a> <a href="#" class="widget-refresh-option"> <i
										class="fa fa-refresh"></i>
								</a>

								</span>
							</div>
							<div class="smart-widget-inner table-responsive">
								<div class="smart-widget-body form-horizontal">																	
                                        <div class="form-group">
                                            <label class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color:red;">*</span> 案卷目录名称</label>
                                            <div class="col-lg-8 col-sm-8 col-xs-12">
                                                <input type="" class="form-control" id=""  placeholder="案卷目录名称">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-2 col-sm-2 col-xs-5 control-label"></label>
                                            <div class="col-lg-8 col-sm-8 col-xs-12">
                                              		  以下内容将生成案卷目录，可进行编辑、完善、调整顺序。
                                            </div>
                                        </div>
                                         <div class="form-group">
                                            <label class="col-lg-2 col-sm-2 col-xs-5 control-label"></label>
                                            <div class="col-lg-8 col-sm-8 col-xs-12">
                                                   <table class="table table-striped no-margin" id="generateTable">
													<tbody id="generateTableTby">
														<tr  v-for="(item, index) in items" :id ="'content'+index">
                                                            <td class="text-left" style="background-color:#f7f7f7;" :id ="'contentTr'+index"  >
                                                            	<input  :id ="'name'+index" class="form-control"   placeholder="勘察笔录及附图" :value='item.name'> </input>
                                                            </td>
                                                            <td style="width:190px; background-color:#f7f7f7;">类型：{{item.typeName}}</td>
                                                            <td class="text-center" style="width:40px; background-color:#23b7e5; cursor:pointer;" data-toggle="modal" data-target="#beizhu">
                                                            	<i title="编辑" class="fa fa-ellipsis-h" style="color:#FFF; font-size:20px;"  v-on:click="changeContent(item.id,item.itemRemark,index)" ></i>
                                                            </td>
                                                            <td class="text-center" style="width:40px; background-color:#efefef; cursor:pointer;">
                                                            	<i title="附件查看" class="fa fa-search" style="color:#23b7e5; font-size:20px;" v-on:click="attachShow(item.id)" ></i>
                                                            </td>
                                                            <td class="text-center" style="width:40px; background-color:#E8E8E8;">
                                                            	<i title="删除"  v-on:click="deleteGenerate(item.id,index)" class="fa fa-times"  style="color:#F00; font-size:20px;"></i>
                                                            </td>
                                           					<td class="text-center" style="width:45px; background-color:#efefef;">
		                                                        <div  v-if="index == 0 ">
		                                                            <i v-if="(index+1) == items.length "></i>
		                                                            <i v-else-if="(index+1) != items.length " title="下移" class="fa fa-caret-down" style="color: #23b7e5; font-size: 20px;  padding-top:25px;cursor:pointer;" v-on:click="chevronDownOrUp(item.id,items[index+1].id,index,1)" ></i>
		                                                        </div>
		                                                        <div v-else-if="(index+1) == items.length ">
		                                                            <i class="fa fa-caret-up" title="上移" style="color: #23b7e5; font-size: 20px; padding-top:25px;cursor:pointer;" v-on:click="chevronDownOrUp(item.id,items[index-1].id,index,0)" ></i>
		                                                        </div>
		                                                        <div v-else>
		                                                             <i class="fa fa-caret-up" title="上移" style="color: #23b7e5; font-size: 20px; padding-top:25px;cursor:pointer;" v-on:click="chevronDownOrUp(item.id,items[index-1].id,index,0)" ></i>
		                                                             <i class="fa fa-caret-down" title="下移" style="color: #23b7e5; font-size: 20px; padding-top:25px;cursor:pointer;" v-on:click="chevronDownOrUp(item.id,items[index+1].id,index,1)" ></i>
		                                                        </div>
																
															</td>
														</tr>
													</tbody>
												</table>
                                            </div>
                                        </div>
								</div>
							</div>
						</div>
					</div>
					
				</div>
			</div>
		</div>
		
	<!-- 附件管理（Modal） -->
	<div class="modal fade" id="ckfj" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			</div>
		</div>
	</div>
		
	<!-- 附件预览  -->
	<div class="modal fade" id="inputImgModeler" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title" id="myModalLabel">附件预览</h4>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				</div>

			</div>
		</div>
	</div>
		
	<!-- 备注（Modal） -->
    <div class="modal fade" id="beizhu" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title" >备注</h4>
                </div>
                <div class="modal-body">
                    <div class="smart-widget-body form-horizontal">
                    	   <form class="form-horizontal"  id="generateContentForm" >
                    	   		<input type="hidden" id="beizhu-index" value=""> 
                    	  		<input type="hidden" id="beizhu-tablesName"   name="tablesName" value="${generateBean.tablesName}">  
                    	   		<input type="hidden" id="beizhu-contentId"   name="contentIdA" value=""> 
	                            <div class="form-group padding-sm">
	                                <div class="col-lg-12">
	                                <!--<label for="exampleInputEmail1">备注说明检查情况及存在的问题</label>-->
	                                <textarea id="beizhu-itemRemark"  name="itemRemark"  class="form-control" rows="8"></textarea>
	                                </div>
	                            </div>
                            </form>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-info" onclick="saveItemRemark()">确定</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
                
            </div>
        </div>
    </div>
    
   <!-- 添加条目（Modal） -->
	<div class="modal fade" id="tjtm" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			
			</div>
		</div>
	</div>
	
	<script type="text/javascript">
		// 保存
		function saveGenerateTypeName(){
			var vueFormData={tablesName:'${generateBean.tablesName}',upOrDowm:'${generateBean.upOrDowm}',objectId:'${generateBean.objectId}',baseId:'${generateBean.baseId}'};
		}
		
		// 添加条目 
		function addGenerateItem(){
			// 注释 模态框清理 
		 	$('#tjtm').on('hide.bs.modal', function () {
				   $(this).removeData("bs.modal");  
			}) 
			var  tablesName = '${generateBean.tablesName}'; 
			var objectId = '${generateBean.objectId}'; 
			var options = {
					remote:WEBPATH+'/atvEasy/add-generate-item?tablesName='+tablesName+'&objectId='+objectId
			};
			$('#tjtm').modal(options);
		}
	</script>
		
</body>
</html>