<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
</head>
<body>
    <script type="text/javascript">
	var  entProtectionBaseIDArr = new Array();
	var  entProtectionBaseNameArr =new Array();
	  
    $(document).ready(function(){
		var entProtectionIDArr =  $("#"+'${entProtectionCodeModelerId}').val();
		var entProtectionNameArr = $("#"+'${entProtectionNameModelerName}').val();
		if(entProtectionIDArr!='' && $.trim(entProtectionIDArr).length > 0){
			var entProtectionBaseIDArrCode = entProtectionIDArr.split(",");
			
			for( i = 0; i< entProtectionBaseIDArrCode.length ; i++){
				entProtectionBaseIDArr.push(entProtectionBaseIDArrCode[i]);
			}
			var nameA = entProtectionNameArr.split(",");
			for( i = 0; i< nameA.length ; i++){
				entProtectionBaseNameArr.push(nameA[i]);
			}
		}
    	 
        $('#hbbwfxwTables').bootstrapTable({
		 method: 'post',
		 dataType: "json", 
		 url:  WEBPATH+'/atvEasy/Illegal-behavior-list',
	     undefinedText : '-',  
	     pagination : true, // 分页  
	     striped : true, // 是否显示行间隔色  
	     cache : false, // 是否使用缓存  
	     pageSize:10, // 设置默认分页为 20
	     pageNumber: 1,
	     queryParamsType: "",
	     checkboxHeader:false,
	     locale:'zh-CN',
	     pageList: [5, 10, 20,30,50], // 自定义分页列表
	     singleSelect: false,
	     contentType: "application/x-www-form-urlencoded;charset=UTF-8",
	     // showColumns : true, // 显示隐藏列  
	     sidePagination: "server", //服务端请求
	     queryParams:function (params) {
	            var temp = {   
          		 	pageNum: params.pageNumber,
                   pageSize: params.pageSize 
	            };
	            return temp;
	     },//参数
	     uniqueId : "id", // 每一行的唯一标识  
		 columns: [
				   {
		        	   field: "",
			           title: "序号",
			           align: 'center',
			           formatter: function(value,row,index){
			        	  return index+1;
				       }
		           },
		           {
			           field: "name",
			           title: "违法行为",
			           align: 'center'
			       },
		           {
			    	   checkbox: true,
			           title: "操作",
			           align: 'center'
		           } 
		 ],
		 responseHandler : function(res) {  
             return {  
                 total : res.total,  
                 rows : res.list  
             };  
       },
       rowStyle:function(row,index) {
       	return {
       		   css: {"cursor": "pointer"}
       		 };
       },
   	 	onCheck: function(row, $element) {
	  		 var rowCode = row.code;
	  		 var rowName = row.name;
			// 全局变量 	
			if($.inArray(rowCode, entProtectionBaseIDArr) == -1){ // 不在加就追加   
				entProtectionBaseIDArr.push(rowCode);
				entProtectionBaseNameArr.push(rowName);
			} 
  		}, 
        onUncheck: function(row, $element) {
        	var rowCode = row.code;
	  		var rowName = row.name;
			// 全局变量 	
			if($.inArray(rowCode, entProtectionBaseIDArr) >-1){ // 若在就移除 
				entProtectionBaseIDArr.splice($.inArray(rowCode,entProtectionBaseIDArr),1); 
				entProtectionBaseNameArr.splice($.inArray(rowName,entProtectionBaseNameArr),1); 
			} 
	     },
	     onUncheckAll: function(row, $element) {
	       			
	     },
	     onCheckAll:function(row, $element) {
	        		
	     },
	     onRefresh: function () {
	        		
	     }, // 双击事件
	     onDblClickRow: function(row, $element){
				    	 
	     }, // 列表加载成功后
	     onLoadSuccess:function(data,e){
	   		 if(entProtectionBaseIDArr !='' && entProtectionBaseIDArr.length>0){ 
	   			var rows = data.rows;
  				if(rows.length>0){ // 若数据，则不许要循环
  					for( var j =0; j < rows.length; j++ ){
  						if($.inArray(rows[j].code, entProtectionBaseIDArr) >-1){
  							$('#hbbwfxwTables').bootstrapTable('check', j);
  						}
  					}
  				}
	   		 }
     	 },
       formatLoadingMessage: function () {
      	   return "玩命加载中...";
       },
       formatNoMatches: function () { //没有匹配的结果
      		   return '无符合条件的记录';
       }
	});
    	
    });
    
    function saveBtnForm(){
		$("#"+'${entProtectionCodeModelerId}').val(entProtectionBaseIDArr.join(","));
		$("#"+'${entProtectionNameModelerName}').val(entProtectionBaseNameArr.join(","));
		var checkFormName =  '${entProtectionCheckFormName}';
		if(checkFormName!='' && $.trim(checkFormName).length > 0){
			$('#'+checkFormName).formValidation('revalidateField', '${entProtectionNameModelerName}');
		}
    }
    </script>
        <!-- 环保部对应违法行为（Modal） -->
		  <div class="modal-header">
              <div style="float:right; margin-top:-5px;">
              <button type="button" class="btn btn-info" data-dismiss="modal" onclick="saveBtnForm()">确定</button>
            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
              </div>
              <h4 class="modal-title" id="myModalLabel">环保部对应违法行为</h4>
          </div>
          <div class="modal-body">
               <table class="table table-striped table-bordered no-margin" id="hbbwfxwTables">
              </table>
          
        </div>
        <div class="modal-footer">
            <!--<button type="button" class="btn btn-info" data-dismiss="modal" onclick="saveBtnForm()">确定</button>
            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>-->
        </div>
    
</body>
</html>