package org.changneng.framework.frameworkweb.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.businessType;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.dbType;
import org.changneng.framework.frameworkbusiness.entity.SceneSysSpecialModel;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.TaskFlow;
import org.changneng.framework.frameworkbusiness.entity.TaskFlowSearch;
import org.changneng.framework.frameworkbusiness.entity.TaskGeneral;
import org.changneng.framework.frameworkbusiness.entity.TaskGeneralSearch;
import org.changneng.framework.frameworkbusiness.service.ISysRolesService;
import org.changneng.framework.frameworkbusiness.service.SpecialTaskService;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
  * 由首页进入-->分配专项执法页面 业务处理逻辑
  * <p>Title:DisSpecialActionController </p>
  * <p>Description: </p>
  * <p>Company: </p> 
  * <AUTHOR>
  * @date 2018年5月29日-上午10:20:25
 */
@Controller
@RequestMapping(value="/dis-special")
public class DisSpecialActionController {

	private Logger logger = LogManager.getLogger(DisSpecialActionController.class);
	
	@Autowired
	private SpecialTaskService specialTaskService;
	
	@Autowired
	private ISysRolesService iSRService;
	
	/**
	 * 跳转到分配专项执法页面
	 * @return
	 * <AUTHOR>
	 * @date 2018年5月29日-上午10:44:14
	 */
	@SysLogPoint(businessType = businessType.COMMON_OPT,dbOptType = dbType.QUERY)
	@RequestMapping("/go-allocation")
	public ModelAndView allocation(Integer launchType,HttpServletRequest request, HttpServletResponse response){
		ModelAndView mav=new ModelAndView("specialTask/allocation-page");
		// 查看当前登录用户是否有访问该菜单的权限
	/*	if(!iSRService.getLoginUserPower(request.getServletPath())){
			mav = new ModelAndView("error/405");
			return mav;
		} */
		if(launchType!=null){ // 0:分配执法  1：直接现场执法
			mav.addObject("launchType",launchType); 
		}else{
			mav.addObject("launchType",0);
		}
		return mav;
		
	}
	
	/**
	 * 适用于我的专项执法
	 * @param taskSearch
	 * @param request
	 * @param response
	 * @return
	 * <AUTHOR>
	 * @date 2018年5月29日-上午11:36:33
	 */
	@RequestMapping(value = "/getDate", method = RequestMethod.POST)
	@ResponseBody
	public PageBean<SceneSysSpecialModel> goSpecialTaskList( HttpServletRequest request,HttpServletResponse response,
			SceneSysSpecialModel specialTask,Integer pageNum,Integer pageSize){
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		PageBean<SceneSysSpecialModel> pageBean = null;
		try {
			 pageBean = specialTaskService.specialTaskApplyList(specialTask,pageNum,pageSize,sysUsers);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info(e);
		}
		return pageBean;
	}
}
