<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:c="http://www.springframework.org/schema/c"
	xmlns:cache="http://www.springframework.org/schema/cache"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jdbc="http://www.springframework.org/schema/jdbc"
	xmlns:jee="http://www.springframework.org/schema/jee"
	xmlns:lang="http://www.springframework.org/schema/lang"
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xmlns:mybatis-spring="http://mybatis.org/schema/mybatis-spring"
	xmlns:p="http://www.springframework.org/schema/p"
	xmlns:task="http://www.springframework.org/schema/task"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.3.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-4.3.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.3.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-4.3.xsd
		http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-4.3.xsd
		http://www.springframework.org/schema/lang http://www.springframework.org/schema/lang/spring-lang-4.3.xsd
		http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.3.xsd
		http://mybatis.org/schema/mybatis-spring http://mybatis.org/schema/mybatis-spring-1.2.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.3.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.3.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.3.xsd">

	<!-- 自动扫描controller包下的所有类，使其认为spring mvc的控制器 -->
	<context:component-scan base-package="org.changneng.framework.frameworkweb.controller" />
	<context:component-scan base-package="org.changneng.framework.frameworkapi.controller" />

	<!-- 加入注解驱动 -->
	<mvc:annotation-driven  validator="validator">  
	    <mvc:message-converters>  
	        <bean id="mappingJacksonHttpMessageConverter" class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">  
	            <property name="supportedMediaTypes">  
	                <list>    
                        <value>application/json;charset=UTF-8</value>    
                        <value>text/html;charset=UTF-8</value><!-- 避免IE出现下载JSON文件的情况 -->    
                    </list>   
                </property>  
            </bean> 
	    </mvc:message-converters>  
	</mvc:annotation-driven>  
	
	<!-- spring验证委托给hibernate validator -->
	<!-- bean级别的校验 方法中的参数bean必须添加@Valid注解，后面紧跟着BindingResult result参数-->
	<bean id="validator" class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean">    
      <property name="providerClass" value="org.hibernate.validator.HibernateValidator"/>    
      <property name="validationMessageSource" ref="validationMessageSource"/>
    </bean>
      
	<!-- 方法级别的校验  要校验的方法所在类必须添加@Validated注解-->
    <bean class="org.springframework.validation.beanvalidation.MethodValidationPostProcessor">
        <!-- 可以引用自己的 validator 配置，在本文中（下面）可以找到 validator 的参考配置，如果不指定则系统使用默认的 -->
        <property name="validator" ref="validator" />
    </bean>
	
	<!-- hibernate validator 国际化 -->
	<bean id="validationMessageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
       	<property name="basenames">
       		 <list>
           	 	<value>classpath:org/changneng/framework/frameworkbusiness/entity/validator/ValidationMessages_zh_CN</value>
            	<value>classpath:org/hibernate/validator/ValidationMessages_zh_CN</value>
         	</list>
         </property>
  	</bean>
	
	<!-- 对模型视图名称的解析，即在模型视图名称添加前后缀 -->
	<bean id="viewResolver" class="org.springframework.web.servlet.view.InternalResourceViewResolver">  
       <property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>  
       <property name="prefix" value="/WEB-INF/page/"/>  
       <property name="suffix" value=".jsp"/>  
    </bean>
	
	<!-- 静态资源的访问 -->  
    <mvc:resources location="/app/" mapping="/app/**"/> 
    <mvc:resources location="/static/" mapping="/static/**"/> 
    
    <!-- 拦截器 -->
    <mvc:interceptors>
       <mvc:interceptor>  
           <mvc:mapping path="/**"/>
           <mvc:exclude-mapping path="/api/**"/>
		   <mvc:exclude-mapping path="/jckh/repair/**"/>
           <bean class="org.changneng.framework.frameworkbusiness.repeatCommit.RepeatCommitInterceptorAdapter"/>  
       </mvc:interceptor>  
       <!-- 拦截对接思路接口，进行ip验证 -->
        <mvc:interceptor>
       		<mvc:mapping path="/ApiData/**"/>
       		<bean class="org.changneng.framework.frameworkbusiness.service.inter.SiLuAuthorityJudgement"></bean>
       </mvc:interceptor>
       <mvc:interceptor>  
           <mvc:mapping path="/api/**"/>  
           <bean class="org.changneng.framework.frameworkbusiness.repeatCommit.AppRepeatCommitInterceptorAdapter"/>  
       </mvc:interceptor> 
       <mvc:interceptor>
    		<mvc:mapping path="/**"/>
    		 <mvc:exclude-mapping path="/api/**"/>
		   <mvc:exclude-mapping path="/jckh/repair/**"/>
    		<bean class="org.changneng.framework.frameworkbusiness.repeatCommit.RepeatCommitInterceptAdapterToken"></bean>
    	</mvc:interceptor>
       <mvc:interceptor>
    		<mvc:mapping path="/**"/>
    		 <mvc:exclude-mapping path="/api/**"/>
		   <mvc:exclude-mapping path="/jckh/repair/**"/>
    		<bean class="org.changneng.framework.frameworkbusiness.repeatCommit.PutSessionValueIntercor"></bean>
    	</mvc:interceptor>
		<!--方法拦截器-->
		<mvc:interceptor>
			<mvc:mapping path="/sysUser/userList"/>
			<mvc:exclude-mapping path="/api/**"/>
			<mvc:exclude-mapping path="/jckh/repair/**"/>
			<bean class="org.changneng.framework.frameworkbusiness.repeatCommit.MethodIntercor"></bean>
		</mvc:interceptor>
    	<!-- 支持页面表单返回回显 -->
    	<mvc:interceptor>
    		<mvc:mapping path="/sysIndex/**"/>
    		<mvc:mapping path="/grkj/**"/>
    		<mvc:mapping path="/randomTaskIssue/**"/>
			<mvc:mapping path="/randomTaskManager/**"/>
    		<mvc:mapping path="/randomTaskWaitIssue/**"/>
    		<mvc:mapping path="/myQuestion/**"/>
    		<mvc:mapping path="/feedBack/**"/>
    		<mvc:mapping path="/sysUser/**"/>
    		<mvc:mapping path="/zfdx/**"/>
    		<mvc:mapping path="/law/**"/>
    		<mvc:mapping path="/behaviour/**"/>
    		<mvc:mapping path="/GPCStandard/**"/>
    		<mvc:mapping path="/dangerArticle/**"/>
    		<mvc:mapping path="/taskGeneral/**"/>
    		<mvc:mapping path="/specialTask/**"/>
    		<mvc:mapping path="/taskManager/**"/>
    		<mvc:mapping path="/SpecialAction/**"/>
    		<mvc:mapping path="/caseInfo/**"/>
    		<mvc:mapping path="/hisoryCase/**"/>
    		<mvc:mapping path="/ajtz/**"/>
    		<mvc:mapping path="/caseManager/**"/>
    		<mvc:mapping path="/intelligenceCheck/**"/>
    		<mvc:mapping path="/swingtagmanage/**"/>
    		<mvc:mapping path="/swingTagRelieve/**"/>
    		<mvc:mapping path="/swingTagQuery/**"/>
    		<mvc:mapping path="/overlaw/**"/>
    		<mvc:mapping path="/refineTemplate/**"/>
    		<mvc:mapping path="/inquiryRecordTemplate/**"/>
    		<mvc:mapping path="/execuDoc/**"/>
    		<mvc:mapping path="/notice/**"/>
    		<mvc:mapping path="/jckh/**"/>
    		<mvc:mapping path="/caseNumberManager/**"/>
    		<mvc:mapping path="/randomOpenManager/**"/>
    		<mvc:mapping path="/taskGenerateOpen/**"/>
    		<mvc:mapping path="/openReview/**"/>
    		<mvc:mapping path="/buildRandomCheck/**"/>
    		<mvc:mapping path="/waitingIssue/**"/>
    		<mvc:mapping path="/administrative/**"/>
    		<mvc:mapping path="/parkManagement/**"/>
    		<bean class="org.changneng.framework.frameworkcore.interceptor.GoBackUrlInterceptor"/>
    	</mvc:interceptor>
   		<mvc:interceptor>
    		<mvc:mapping path="/api/**"/>
    		<mvc:exclude-mapping path="/api/auth/login"/>
    		<mvc:exclude-mapping path="/api/auth/getCurTime"/>
    		<mvc:exclude-mapping path="/api/taskDistribute/updateFile"/>
    		<mvc:exclude-mapping path="/api/taskManager/pictureUpload"/>
    		<mvc:exclude-mapping path="/api/taskManager/picInfoSave"/>
    		<mvc:exclude-mapping path="/api/taskManager/fjInfoSave"/>
    		<mvc:exclude-mapping path="/api/localExamine/saveLocalFile"/>
    		<mvc:exclude-mapping path="/api/auth/getVersion"/>
    		<mvc:exclude-mapping path="/api/auth/appDownload"/>
    		<mvc:exclude-mapping path="/api/auth/updateAvatarFile"/>
    		<mvc:exclude-mapping path="/api/taskDistribute/upChickItemAdjuct"/>
    		<mvc:exclude-mapping path="/api/taskDistribute/upSurveyPicsAdjuct"/>
    		<mvc:exclude-mapping path="/api/taskManager/uploadVideo"/>
    		<mvc:exclude-mapping path="/api/taskManager/uploadRecord"/>
    	    <mvc:exclude-mapping path="/api/caseQr/add-file"/>
    	    <mvc:exclude-mapping path="/api/docQr/uploadFile"/>
    	    <mvc:exclude-mapping path="/api/caseQr/generate-file-upload"/>
    	    <mvc:exclude-mapping path="/api/lawCircle/uploadImg"/>
    	    <mvc:exclude-mapping path="/api/auth/uploadLawEnforceCredential"/>
    	    <mvc:exclude-mapping path="/api/event/**"/>
			<mvc:exclude-mapping path="/api/zf/**"/>
			<mvc:exclude-mapping path="/api/Msg/sendSMCodeForBak"/>
			<mvc:exclude-mapping path="/api/Msg/checkSMCode"/>
    	    <!-- 测试结束后注掉 -->
            <mvc:exclude-mapping path="/api/dispatch/**"/>
			<mvc:exclude-mapping path="/api/complaint/**"/>
           <!--<mvc:exclude-mapping path="/api/caseQr/save-items-info"/> -->
            <!-- ./测试结束后注掉 -->
    		<bean class="org.changneng.framework.frameworkapi.controller.GlobalHandlerInterceptor"/>
    	</mvc:interceptor>
    	
    </mvc:interceptors>
	
	<!-- aop注解扫描配置 -->
    <aop:aspectj-autoproxy proxy-target-class="true" /> 
	
	<!--使spring支持cors跨越访问-->
	<mvc:cors>
		<mvc:mapping path="/api/**" 
		allowed-origins="*"
		allowed-methods="*"
		allowed-headers="registerId,secret,login_token,access_token,exp_time,Access-Control-Allow-Methods,Access-Control-Allow-Origin,Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers" 
		allow-credentials="true"/>
		<!-- 对接的数据接口 -->
		 <mvc:mapping path="/ApiData/**" 
		allowed-origins="*"
		allowed-methods="*"
		allowed-headers="Access-Control-Allow-Methods,Access-Control-Allow-Origin,Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers" 
		allow-credentials="true"/>
		<mvc:mapping path="/silupage/**" 
		allowed-origins="*"
		allowed-methods="*"
		allowed-headers="Access-Control-Allow-Methods,Access-Control-Allow-Origin,Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers" 
		allow-credentials="true"/>  
		<mvc:mapping path="/zhjg/**" 
		allowed-origins="*"
		allowed-methods="*"
		allowed-headers="Access-Control-Allow-Methods,Access-Control-Allow-Origin,Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers" 
		allow-credentials="true"/>  
		<mvc:mapping path="/largeScreenStatistical/**" 
		allowed-origins="*"
		allowed-methods="*"
		allowed-headers="Access-Control-Allow-Methods,Access-Control-Allow-Origin,Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers" 
		allow-credentials="true"/>

		<mvc:mapping path="/onepictureLawCase/**"
					 allowed-origins="*"
					 allowed-methods="*"
					 allowed-headers="Access-Control-Allow-Methods,Access-Control-Allow-Origin,Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers"
					 allow-credentials="true"/>

		<mvc:mapping path="/onePicture/**"
					 allowed-origins="*"
					 allowed-methods="*"
					 allowed-headers="Access-Control-Allow-Methods,Access-Control-Allow-Origin,Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers"
					 allow-credentials="true"/>


	<mvc:mapping path="/xfzf/**"
					 allowed-origins="*"
					 allowed-methods="*"
					 allowed-headers="Access-Control-Allow-Methods,Access-Control-Allow-Origin,Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers"
					 allow-credentials="true"/>

		<mvc:mapping path="/actionFileManager/**" 
		allowed-origins="*"
		allowed-methods="*"
		allowed-headers="registerId,secret,login_token,access_token,exp_time,Access-Control-Allow-Methods,Access-Control-Allow-Origin,Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers" 
		allow-credentials="true"/>
	</mvc:cors>
	
	<!-- spring多文件上传 -->
	<bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<property name="defaultEncoding">
			<value>UTF-8</value>
		</property>
		<property name="maxUploadSize">
			<!-- 上传文件大小限制为50M，50*1024*1024 -->
			<value>52428800</value>
		</property>
			<!-- 内存大小限制为8M，8*1024*1024 -->
		<property name="maxInMemorySize">
			<value>8388608</value>
		</property>
		<property name="resolveLazily" value="true"/>  
	</bean>
	
</beans>