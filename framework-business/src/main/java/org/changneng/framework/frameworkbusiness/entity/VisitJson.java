package org.changneng.framework.frameworkbusiness.entity;
/**
 * 
 * <AUTHOR>
 *一园一档   网格数据调用传参使用
 */
public class VisitJson {
	 // 输入 网格code
	  private String areaCode;
	  // 输入 网格级别
	  private String areaLeavel;
	  // 输入 执法对象ID
	  private String lawObjectId;
	public String getAreaCode() {
		return areaCode;
	}
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public String getAreaLeavel() {
		return areaLeavel;
	}
	public void setAreaLeavel(String areaLeavel) {
		this.areaLeavel = areaLeavel;
	}
	public String getLawObjectId() {
		return lawObjectId;
	}
	public void setLawObjectId(String lawObjectId) {
		this.lawObjectId = lawObjectId;
	}
	  
	  
}
