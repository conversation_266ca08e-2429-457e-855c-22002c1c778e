package org.changneng.framework.frameworkbusiness.entity.silu;

import java.math.BigDecimal;
import java.util.Date;

public class SPointPositionInfo {
    private String outputId;

    private String outfallType;

    private String code;

    private String name;

    private String pos;

    private String gafTypeCode;

    private String gorCode;

    private String fuelCode;

    private String burnCode;

    private String symbolStyle;

    private Long peId;

    private Integer seqNo;

    private String licenceCode;

    private BigDecimal allowPluLet;

    private String status;

    private String hiddenOutput;

    private Integer isexport;

    private String stockShow;

    private BigDecimal longitude;

    private BigDecimal latitude;

    private Integer airCoefficient;

    private String singleOutput;

    private String boilerType;

    private String csn;

    private String pwd;

    private String ipAddress;

    private Integer port;

    private Long cyc;

    private String product;

    private String contact;

    private String contactNum;

    private String isPutDaily;

    private String isPutHour;

    private String isPutMin;

    private String upTran;

    private String downTran;

    private String etongTran;

    private BigDecimal unitTranslate;

    private Date updateTime;

    private Long updateUserId;

    private Date insertTime;

    private Long insertUserId;

    private String river;

    private String direction;

    private Long operator;

    private String isProvinceConcerned;

    private String reportStop;

    private String enableStatus;

    private String siteType;

    private BigDecimal actualDailyCapacity;

    private Date firstUploadData;

    private String isCityConcerned;

    private String isAreaConcerned;

    public String getOutputId() {
        return outputId;
    }

    public void setOutputId(String outputId) {
        this.outputId = outputId == null ? null : outputId.trim();
    }

    public String getOutfallType() {
        return outfallType;
    }

    public void setOutfallType(String outfallType) {
        this.outfallType = outfallType == null ? null : outfallType.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getPos() {
        return pos;
    }

    public void setPos(String pos) {
        this.pos = pos == null ? null : pos.trim();
    }

    public String getGafTypeCode() {
        return gafTypeCode;
    }

    public void setGafTypeCode(String gafTypeCode) {
        this.gafTypeCode = gafTypeCode == null ? null : gafTypeCode.trim();
    }

    public String getGorCode() {
        return gorCode;
    }

    public void setGorCode(String gorCode) {
        this.gorCode = gorCode == null ? null : gorCode.trim();
    }

    public String getFuelCode() {
        return fuelCode;
    }

    public void setFuelCode(String fuelCode) {
        this.fuelCode = fuelCode == null ? null : fuelCode.trim();
    }

    public String getBurnCode() {
        return burnCode;
    }

    public void setBurnCode(String burnCode) {
        this.burnCode = burnCode == null ? null : burnCode.trim();
    }

    public String getSymbolStyle() {
        return symbolStyle;
    }

    public void setSymbolStyle(String symbolStyle) {
        this.symbolStyle = symbolStyle == null ? null : symbolStyle.trim();
    }

    public Long getPeId() {
        return peId;
    }

    public void setPeId(Long peId) {
        this.peId = peId;
    }

    public Integer getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(Integer seqNo) {
        this.seqNo = seqNo;
    }

    public String getLicenceCode() {
        return licenceCode;
    }

    public void setLicenceCode(String licenceCode) {
        this.licenceCode = licenceCode == null ? null : licenceCode.trim();
    }

    public BigDecimal getAllowPluLet() {
        return allowPluLet;
    }

    public void setAllowPluLet(BigDecimal allowPluLet) {
        this.allowPluLet = allowPluLet;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getHiddenOutput() {
        return hiddenOutput;
    }

    public void setHiddenOutput(String hiddenOutput) {
        this.hiddenOutput = hiddenOutput == null ? null : hiddenOutput.trim();
    }

    public Integer getIsexport() {
        return isexport;
    }

    public void setIsexport(Integer isexport) {
        this.isexport = isexport;
    }

    public String getStockShow() {
        return stockShow;
    }

    public void setStockShow(String stockShow) {
        this.stockShow = stockShow == null ? null : stockShow.trim();
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public Integer getAirCoefficient() {
        return airCoefficient;
    }

    public void setAirCoefficient(Integer airCoefficient) {
        this.airCoefficient = airCoefficient;
    }

    public String getSingleOutput() {
        return singleOutput;
    }

    public void setSingleOutput(String singleOutput) {
        this.singleOutput = singleOutput == null ? null : singleOutput.trim();
    }

    public String getBoilerType() {
        return boilerType;
    }

    public void setBoilerType(String boilerType) {
        this.boilerType = boilerType == null ? null : boilerType.trim();
    }

    public String getCsn() {
        return csn;
    }

    public void setCsn(String csn) {
        this.csn = csn == null ? null : csn.trim();
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd == null ? null : pwd.trim();
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress == null ? null : ipAddress.trim();
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public Long getCyc() {
        return cyc;
    }

    public void setCyc(Long cyc) {
        this.cyc = cyc;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product == null ? null : product.trim();
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact == null ? null : contact.trim();
    }

    public String getContactNum() {
        return contactNum;
    }

    public void setContactNum(String contactNum) {
        this.contactNum = contactNum == null ? null : contactNum.trim();
    }

    public String getIsPutDaily() {
        return isPutDaily;
    }

    public void setIsPutDaily(String isPutDaily) {
        this.isPutDaily = isPutDaily == null ? null : isPutDaily.trim();
    }

    public String getIsPutHour() {
        return isPutHour;
    }

    public void setIsPutHour(String isPutHour) {
        this.isPutHour = isPutHour == null ? null : isPutHour.trim();
    }

    public String getIsPutMin() {
        return isPutMin;
    }

    public void setIsPutMin(String isPutMin) {
        this.isPutMin = isPutMin == null ? null : isPutMin.trim();
    }

    public String getUpTran() {
        return upTran;
    }

    public void setUpTran(String upTran) {
        this.upTran = upTran == null ? null : upTran.trim();
    }

    public String getDownTran() {
        return downTran;
    }

    public void setDownTran(String downTran) {
        this.downTran = downTran == null ? null : downTran.trim();
    }

    public String getEtongTran() {
        return etongTran;
    }

    public void setEtongTran(String etongTran) {
        this.etongTran = etongTran == null ? null : etongTran.trim();
    }

    public BigDecimal getUnitTranslate() {
        return unitTranslate;
    }

    public void setUnitTranslate(BigDecimal unitTranslate) {
        this.unitTranslate = unitTranslate;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public Long getInsertUserId() {
        return insertUserId;
    }

    public void setInsertUserId(Long insertUserId) {
        this.insertUserId = insertUserId;
    }

    public String getRiver() {
        return river;
    }

    public void setRiver(String river) {
        this.river = river == null ? null : river.trim();
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction == null ? null : direction.trim();
    }

    public Long getOperator() {
        return operator;
    }

    public void setOperator(Long operator) {
        this.operator = operator;
    }

    public String getIsProvinceConcerned() {
        return isProvinceConcerned;
    }

    public void setIsProvinceConcerned(String isProvinceConcerned) {
        this.isProvinceConcerned = isProvinceConcerned == null ? null : isProvinceConcerned.trim();
    }

    public String getReportStop() {
        return reportStop;
    }

    public void setReportStop(String reportStop) {
        this.reportStop = reportStop == null ? null : reportStop.trim();
    }

    public String getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(String enableStatus) {
        this.enableStatus = enableStatus == null ? null : enableStatus.trim();
    }

    public String getSiteType() {
        return siteType;
    }

    public void setSiteType(String siteType) {
        this.siteType = siteType == null ? null : siteType.trim();
    }

    public BigDecimal getActualDailyCapacity() {
        return actualDailyCapacity;
    }

    public void setActualDailyCapacity(BigDecimal actualDailyCapacity) {
        this.actualDailyCapacity = actualDailyCapacity;
    }

    public Date getFirstUploadData() {
        return firstUploadData;
    }

    public void setFirstUploadData(Date firstUploadData) {
        this.firstUploadData = firstUploadData;
    }

    public String getIsCityConcerned() {
        return isCityConcerned;
    }

    public void setIsCityConcerned(String isCityConcerned) {
        this.isCityConcerned = isCityConcerned == null ? null : isCityConcerned.trim();
    }

    public String getIsAreaConcerned() {
        return isAreaConcerned;
    }

    public void setIsAreaConcerned(String isAreaConcerned) {
        this.isAreaConcerned = isAreaConcerned == null ? null : isAreaConcerned.trim();
    }
}