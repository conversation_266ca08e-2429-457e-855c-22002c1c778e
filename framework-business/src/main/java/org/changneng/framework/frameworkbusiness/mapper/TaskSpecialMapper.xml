<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.TaskSpecialMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.TaskSpecial">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SPECIAL_ACTION_ID" jdbcType="VARCHAR" property="specialActionId" />
    <result column="TASK_ID" jdbcType="VARCHAR" property="taskId" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, SPECIAL_ACTION_ID, TASK_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TASK_SPECIAL
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from TASK_SPECIAL
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.TaskSpecial">
    insert into TASK_SPECIAL (ID, SPECIAL_ACTION_ID, TASK_ID
      )
    values (#{id,jdbcType=VARCHAR}, #{specialActionId,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.TaskSpecial">
      <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	 </selectKey>
    insert into TASK_SPECIAL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="specialActionId != null">
        SPECIAL_ACTION_ID,
      </if>
      <if test="taskId != null">
        TASK_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="specialActionId != null">
        #{specialActionId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.TaskSpecial">
    update TASK_SPECIAL
    <set>
      <if test="specialActionId != null">
        SPECIAL_ACTION_ID = #{specialActionId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        TASK_ID = #{taskId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.TaskSpecial">
    update TASK_SPECIAL
    set SPECIAL_ACTION_ID = #{specialActionId,jdbcType=VARCHAR},
      TASK_ID = #{taskId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <!-- updateTaskSpecialMapperList -->
   <update id="updateTaskSpecialMapperList" parameterType="org.changneng.framework.frameworkbusiness.entity.TaskSpecial">
    update TASK_SPECIAL
    set SPECIAL_ACTION_ID = #{specialActionIds,jdbcType=VARCHAR}
    where TASK_ID = #{taskId}
  </update>
  <!-- deleteTaskSpecialByTaskId 根据任务id删除专项行动记录信息 -->
   <delete id="deleteTaskSpecialByTaskId" parameterType="java.lang.String">
    delete from TASK_SPECIAL
    where TASK_ID = #{taskId}
  </delete>
  <select id="selectByTaskId" parameterType="java.lang.String" resultType="java.lang.Integer">
    select 
    count(*)
    from TASK_SPECIAL
    where TASK_ID = #{taskId,jdbcType=VARCHAR}  AND  SPECIAL_ACTION_ID = #{specialActionId,jdbcType=VARCHAR}
  </select>
  <select id="selectBatchAssociated" parameterType="java.lang.String" resultType="org.changneng.framework.frameworkbusiness.entity.TaskSpecial">
    select 
    	*
    from TASK_SPECIAL
    where TASK_ID = #{taskId,jdbcType=VARCHAR} AND  SPECIAL_ACTION_ID = #{specialActionId,jdbcType=VARCHAR}
  </select>
   <delete id="delTaskSpecial" parameterType="java.lang.String">
    delete from TASK_SPECIAL
    where TASK_ID = #{taskId,jdbcType=VARCHAR} AND  SPECIAL_ACTION_ID = #{specialActionId,jdbcType=VARCHAR}
  </delete>
  <select id="selectTaskIdBySpecialActionId" parameterType="java.lang.String" resultType="java.lang.String">
  		select DISTINCT task_id from TASK_SPECIAL where SPECIAL_ACTION_ID=#{specialActionId,jdbcType=VARCHAR}
  </select>
  <select id="selectAssociatedTaskCountBySpecialActionIds" parameterType="java.util.List" resultType="java.lang.Integer">
  		select count(DISTINCT task_id) from TASK_SPECIAL
  		where SPECIAL_ACTION_ID in
  		<foreach collection="specialActionIds" item="specialActionId" open="(" close=")" separator=",">
  			#{specialActionId}
  		</foreach>
  
  </select>
</mapper>