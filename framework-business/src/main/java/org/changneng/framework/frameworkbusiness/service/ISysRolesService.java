package org.changneng.framework.frameworkbusiness.service;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.SysRoles;
import org.changneng.framework.frameworkbusiness.entity.ZTreeNodeBean;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

public interface ISysRolesService {
	
	/**
	 * 角色-添加
	 * @return
	 * @throws Exception
	 */
	public int addRole(SysRoles sysrole) throws Exception;
	
	/**
	 * 角色-编辑
	 * @return
	 * @throws Exception
	 */
	public int editRole(SysRoles sysrole) throws Exception;
	
	/**
	 * 角色-集合分页查询-根据角色属性条件
	 * @param sysrole
	 * @return
	 * @throws Exception
	 */
	public PageBean<SysRoles> querySysRolesList(SysRoles sysrole,int pageNum,int pageSize);
	
	/**
	 * 角色-集合查询
	 * @param sysrole
	 * @return
	 * @throws Exception
	 */
	public List<SysRoles> queryRolesList(SysRoles sysrole);
	
	/**
	 * 角色-逻辑删除-根据ID
	 * @param roleId
	 * @return
	 * @throws Exception
	 */
	public int updateRoleState(String roleId);
	
	/**
	 * 角色-查询-根据ID
	 * @param roleId
	 * @return
	 * @throws Exception
	 */
	public SysRoles queryRoleById(String roleId) throws Exception;
	
	/**
	 * 加载权限树
	 * @return
	 * @throws Exception
	 */
	public List<ZTreeNodeBean> loadPowerTree(String roleID)throws Exception;
	
	/**
	 * 设置保存权限
	 * @return
	 * @throws Exception
	 */
	public int setPowersForRoels(String roleID,String poweIDs);
	
	/**
	 * 查看当前登录用户是否有查看该菜单的权限
	 * @param actionUrl 访问地址请求Url路径
	 * @return 
	 * <AUTHOR>
	 * @date 2018年6月15日-上午11:11:14
	 */
	boolean getLoginUserPower(String actionUrl);

}
