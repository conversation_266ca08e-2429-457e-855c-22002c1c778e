package org.changneng.framework.frameworkbusiness.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.LawCircle;
import org.changneng.framework.frameworkbusiness.entity.LawCircleFiles;

public interface LawCircleFilesMapper {
    int deleteByPrimaryKey(String id);

    int insert(LawCircleFiles record);

    int insertSelective(LawCircleFiles record);

    LawCircleFiles selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(LawCircleFiles record);

    int updateByPrimaryKey(LawCircleFiles record);

	List<LawCircleFiles> selectByLawCircleId(@Param("lawCircleId")String lawCircleId);

	void updateStateByLawCircleId(@Param("lawCircleId")String lawCircleId);

	List<LawCircleFiles> selectSrcFilesInfoByLawCircleId(@Param("lawCircleId")String lawCircleId);

	LawCircleFiles selectSrcLawCircleFileByFileId(@Param("id")String id);

	LawCircleFiles selectSmallLawCircleFileByOriginalImgId(@Param("originalImgId")String originalImgId);

	void cleanFileInfos();

	List<LawCircleFiles> selectAllDeletedFileInfoByLawCircleId(@Param("lawCircleId")String lawCircleId);

	List<String> getUrlList();
    
}