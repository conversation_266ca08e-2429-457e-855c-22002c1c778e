package org.changneng.framework.frameworkbusiness.dao.filecase;

import java.util.HashMap;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.IndustryType;
import org.changneng.framework.frameworkbusiness.entity.LargeScreenStatistical;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.TcDictionary;
import org.changneng.framework.frameworkbusiness.entity.filecase.AjtzSearchBean;
import org.changneng.framework.frameworkbusiness.entity.filecase.AjtzSearchResultBean;
import org.changneng.framework.frameworkbusiness.entity.filecase.AtvSanctionCase;
import org.changneng.framework.frameworkbusiness.entity.filecase.AtvSanctionCaseWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.WenHaoSearchResult;

public interface AtvSanctionCaseMapper {
    int deleteByPrimaryKey(String id);

    int insert(AtvSanctionCaseWithBLOBs record);

    int insertSelective(AtvSanctionCaseWithBLOBs record);

    AtvSanctionCaseWithBLOBs selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(AtvSanctionCaseWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(AtvSanctionCaseWithBLOBs record);

    int updateByPrimaryKey(AtvSanctionCase record);
    
    
    /**
     * 完整版的updateSelective
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective1(AtvSanctionCaseWithBLOBs record);
    
    /**
     * 根据主干信息ID查询出关联的简易程序
     * @param caseId
     * @return
     */
    AtvSanctionCaseWithBLOBs getEasyAtvSanctionCaseByCaseId(@Param("caseId") String caseId);
    
    /**
     * 逻辑删除
     * @param id
     * @return
     */
    int deleteByPrimaryKeyByID(@Param("id")  String id);
    
    /**
     * 简易程序 校验 立案号、决定书文号、案卷号 ，在同区划下是否唯一 
     * @param whereSql
     * @return
     */
    Integer checkAtvSanctionCaseNumber(@Param("value") String value);
    
    /**
     * 根据caseId查询一条一般行政处罚的信息
     * @param caseId
     * @return
     */
    AtvSanctionCaseWithBLOBs getGeneralAtvSanctionCaseByCaseId(@Param("caseId") String caseId);
    
    /**
     * 简易程序 通过caseId查询该案件是否已经存在
     * 返回实体类：仅有id   lhl
     * @param caseId
     * @return
     */
    AtvSanctionCaseWithBLOBs getAtvSanctionCaseWithBLOBsOneByCaseId(@Param("caseId") String caseId);
    /**
     * 查编号重复
     * @param filingNumber
     * @param decisionNumber
     * @param closeCaseNumber
     * @return
     */
    List<AtvSanctionCase> checkRepeat(@Param("filingNumber")String filingNumber,@Param("decisionNumber")String decisionNumber,
    		@Param("closeCaseNumber")String closeCaseNumber,@Param("creatorUserArea")String creatorUserArea);

    /**
     * 案件台账查询
     * @param searchBean 查询条件bean
     * @param belongAreaCode 用户所属区划
     * @return
     */
	List<AjtzSearchResultBean> selectCaseAccountsInfo(@Param("searchBean")AjtzSearchBean searchBean, @Param("belongAreaCode") String belongAreaCode);

	/**
	 * 根据表名和id查询caseid
	 * @param tablesName
	 * @param objectId
	 * @return
	 */
	String selectCaseIdById(@Param("tableName")String tablesName, @Param("id")String objectId);

	/**
	 * 根据caseid查询一条有效数据的id
	 */
	String selectIdByCaseId(String caseId);

	/**
	 * 根据caseid查询一条有效数据的id
	 * @param caseId
	 * @return
	 */
	AtvSanctionCase getAtvSanctionCaseByCaseId(@Param("caseId")String caseId);
	
	/**
	 * 查询出表中需要对接的数据集合
	 * @return
	 */
	List<AtvSanctionCaseWithBLOBs> selectSendInfoList();
	/**
	 * 查询出建设项目的集合
	 * @return
	 */
	List<AtvSanctionCaseWithBLOBs> selectConstructInfoList();
	
	
	/**
	 * 依据案件基本信息为纬度，借用综合台账行政处罚实体类
	 * @param searchBean
	 * @return
	 * <AUTHOR>
	 * @date 2017年11月16日-下午3:25:13
	 */
	List<AjtzSearchResultBean> selectCaseBaseInfoByObjectId(@Param("searchBean")AjtzSearchBean searchBean);
	
	/**
	 * 查询昨天对接失败的集合
	 * @param yesterday
	 * @return
	 * <AUTHOR>
	 */
	List<AtvSanctionCaseWithBLOBs> selectFailDockList(@Param("yesterday")String yesterday);
	
	/**
	 * 查询昨天对接失败的建设项目
	 * @param yesterday
	 * @return
	 * <AUTHOR>
	 */
	List<AtvSanctionCaseWithBLOBs> selectFailDockConstructList(@Param("yesterday")String yesterday);
 
	
	/**
	 * 初始化信息,查看现有的简易、一般，进行初始化
	 * @return
	 * <AUTHOR>
	 * @date 2018年3月1日-下午6:07:18
	 */
	List<AtvSanctionCaseWithBLOBs> selectAtvSanctionCaseWithBLOBsForCaseNumber(@Param("code") String code,@Param("caseType")String caseType);
	
	/**
	 * 
     * 根据基本信息创建人区划查询部门表中该部门是否垂管完成。
	 * @param belong_areaCode
	 * @param manageNumberType
	 * @return
	 */
	WenHaoSearchResult selectIsApplicationByAreaCode(@Param("belongAreaCode")String belongAreaCode,@Param("manageNumberType")String manageNumberType);
	/**
	 * 根据id将处罚类型为否的文号信息清空
	 * @param record
	 * @return
	 */
	int updateWenHaoInfoById(AtvSanctionCaseWithBLOBs record);
	
	/**
	 * 简易、一般行政处罚阻断性校验   1：基本信息，2：简易行政处罚，3：一般行政处罚，4：行政命令，5：查封扣押，6：限产停产，7：行政拘留，
						 8：环境污染犯罪，9：申请法院强制执行，10：其他移送，11：按日计罚
	 * <br>拼装sql返回HashMap集合
	 * @return
	 * <AUTHOR>
	 * @date 2018年6月11日-下午4:54:45
	 */
	HashMap<String, String> selectBlockCheck(@Param("assembleSql")String assembleSql);
	/**
	 * 一键生成案卷最终 案卷维护附件对接状态和附件url
	 * @param sql
	 * @return
	 */
	void updateBySql(@Param("sql")String sql);

	List<LargeScreenStatistical> selectAtvSanctionCaseAnalyze(@Param("year")String year, @Param("tcDictionaries")List<TcDictionary> tcDictionaries);

	List<LargeScreenStatistical> selectPunishmentMoneyCountAnalyze(@Param("year")String year);

	List<LargeScreenStatistical> getAtvSanctionLawObjectJobTypeCaseCount(@Param("year")String year, @Param("industryTypes")List<IndustryType> industryTypes);
	//根据主案件id查询信息；
	AtvSanctionCaseWithBLOBs getBeanByCaseId(@Param("caseId") String caseId);

	/**
	 * 对接发改委数据
	 * @param searchBean 查询条件bean
	 * @return
	 */
	List<AjtzSearchResultBean> getfgwData(@Param("searchBean")AjtzSearchBean searchBean,@Param("sysUsers")SysUsers sysUsers);

    List<AjtzSearchResultBean> selectCaseBaseInfoByObjectName(@Param("searchBean")AjtzSearchBean searchBean);

	List<AjtzSearchResultBean> queryPwaseBaseList(@Param("searchBean") AjtzSearchBean searchBean);
}