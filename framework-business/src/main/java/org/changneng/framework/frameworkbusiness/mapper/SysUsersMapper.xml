<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.SysUsersMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.SysUsers">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="LOGINNAME" jdbcType="VARCHAR" property="loginname" />
    <result column="PASSWORD" jdbcType="VARCHAR" property="password" />
    <result column="USERNAME" jdbcType="VARCHAR" property="username" />
    <result column="CARDID" jdbcType="VARCHAR" property="cardid" />
    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="JOB_ID" jdbcType="VARCHAR" property="jobId" />
    <result column="JOB_NAME" jdbcType="VARCHAR" property="jobName" />
    <result column="ACCOUNT_NON_SYSTEM" jdbcType="DECIMAL" property="accountNonSystem" />
    <result column="SYS_USER_NUMBER" jdbcType="VARCHAR" property="sysUserNumber" />
    <result column="LOGIN_NICKNAME" jdbcType="VARCHAR" property="loginNickname" />
    <result column="ORG_BELONG_DEPART_ID" jdbcType="VARCHAR" property="orgBelongDepartId" />
    <result column="ORG_BELONG_DEPART_NAME" jdbcType="VARCHAR" property="orgBelongDepartName" />
    <result column="ACCOUNT_NON_TEAM" jdbcType="DECIMAL" property="accountNonTeam" />
    <result column="ACCOUNT_NON_LAWOFFICER" jdbcType="DECIMAL" property="accountNonLawofficer" />
    <result column="ACCOUNT_NON_STATION" jdbcType="DECIMAL" property="accountNonStation" />
    <result column="LAW_DEVICE_ID" jdbcType="VARCHAR" property="lawDeviceId" />
    <result column="LAW_ENFORC_ID" jdbcType="VARCHAR" property="lawEnforcId" />
    <result column="LAW_EFFECTIVE_DATE" jdbcType="TIMESTAMP" property="lawEffectiveDate" />
    <result column="LAW_INVALID_DATE" jdbcType="TIMESTAMP" property="lawInvalidDate" />
    <result column="LAW_CERTIFICATE_ENABLED" jdbcType="DECIMAL" property="lawCertificateEnabled" />
    <result column="SUPERVISION_CERTIFICATE_ID" jdbcType="VARCHAR" property="supervisionCertificateId" />
    <result column="SUPERVISION_EFFECTIVE_DATE" jdbcType="TIMESTAMP" property="supervisionEffectiveDate" />
    <result column="SUPERVISION_INVALID_DATE" jdbcType="TIMESTAMP" property="supervisionInvalidDate" />
    <result column="SUPERVISIO_CERTIFICATE_ENABLED" jdbcType="DECIMAL" property="supervisioCertificateEnabled" />
    <result column="BELONG_DEPARTMENT_ID" jdbcType="VARCHAR" property="belongDepartmentId" />
     <result column="DEPARTMENT_NAME" jdbcType="VARCHAR" property="belongDepartmentName" />
    <result column="BELONG_DEPARTMENT_NAME" jdbcType="VARCHAR" property="belongDepartmentName" />
    <result column="BELONG_AREA_ID" jdbcType="VARCHAR" property="belongAreaId" />
    <result column="BELONG_AREA_NAME" jdbcType="VARCHAR" property="belongAreaName" />
    <result column="DT_CREATE" jdbcType="TIMESTAMP" property="dtCreate" />
    <result column="LAST_LOGIN" jdbcType="TIMESTAMP" property="lastLogin" />
    <result column="DENDLINE" jdbcType="TIMESTAMP" property="dendline" />
    <result column="LOGIN_IP" jdbcType="VARCHAR" property="loginIp" />
    <result column="ENABLED" jdbcType="DECIMAL" property="userEnabled" />
    <result column="ACCOUNT_NON_EXPIRED" jdbcType="DECIMAL" property="userNonExpired" />
    <result column="ACCOUNT_NON_LOCKED" jdbcType="DECIMAL" property="userNonLocked" />
    <result column="CREDENTIALS_NON_EXPIRED" jdbcType="DECIMAL" property="credentialsUserNonExpired" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="LAST_UPDATE_USER" jdbcType="VARCHAR" property="lastUpdateUser" />
    <result column="DEPARTMENT_NUMBER" jdbcType="VARCHAR" property="departmentNumber" />
    <result column="IS_LOGIN_TODAY" jdbcType="VARCHAR" property="isLoginToday" />
    <result column="IS_SYSADMIN" jdbcType="DECIMAL" property="isSysadmin" />
    <result column="IS_SNS" jdbcType="DECIMAL" property="isSns" />
    <result column="AVATAR_URL" jdbcType="VARCHAR" property="avatarUrl" />
    <result column="FILE_ID" jdbcType="VARCHAR" property="fileId" />
    <result column="IS_DEFAULT_AVATAR" jdbcType="DECIMAL" property="isDefaultAvatar" />
    <result column="DEFAULT_AVATAR_URL" jdbcType="VARCHAR" property="defaultAvatarUrl" />
    <!-- 2017-07-05 新增一个字段 开始 -->
        <result column="ACCOUNT_NON_POST" jdbcType="DECIMAL" property="accountNonPost" />
    <!-- 2017-07-05 新增一个字段 结束 -->
  <!--   <collection property="roleList" ofType="org.changneng.framework.frameworkbusiness.entity.SysRoles">
    	 <id column="role_id" property="roleId" jdbcType="INTEGER" />
	     <result column="role_name" property="roleName" jdbcType="VARCHAR" />
	      <result column="role_desc" property="roleDesc" jdbcType="VARCHAR" />
    </collection> -->
    <!-- 2018-07-17 对接思路用户接口新增字段 开始 -->
    <result column="BIRTHDAY" jdbcType="TIMESTAMP" property="birthday" />
    <result column="GENDAR" jdbcType="DECIMAL" property="gendar" />
    <result column="EDUCATION" jdbcType="VARCHAR" property="education" />
    <result column="SILU_CREATE_DATE" jdbcType="TIMESTAMP" property="siluCreateDate" />
    <result column="IS_SILU" jdbcType="DECIMAL" property="isSilu" />
    <result column="SILU_ID" jdbcType="VARCHAR" property="siluId" />
    <result column="SILU_AREA_CODE" jdbcType="VARCHAR" property="siluAreaCode" />
    <!-- 2018-07-17 对接思路用户接口新增字段 结束 -->
     <result column="START_LAW_ENFORCE_JOB_YEAR" jdbcType="VARCHAR" property="startLawEnforceJobYear" />
     <result column="GOOD_AT_LAW_ENFORCE_INDUSTRY" jdbcType="VARCHAR" property="goodAtLawEnforceIndustry" />
     <result column="LAW_ENFORCE_CREDENTIAL" jdbcType="VARCHAR" property="lawEnforceCredential" />
     <result column="LAW_ENFORCE_CREDENTIAL_ID" jdbcType="VARCHAR" property="lawEnforceCredentialId" />
     <result column="departmentName" jdbcType="VARCHAR" property="departmentName" />     
  </resultMap>
  <resultMap id="AppSysUsers" type="org.changneng.framework.frameworkbusiness.entity.AppSysUsers">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="LOGINNAME" jdbcType="VARCHAR" property="loginname" />
    <result column="law_enforc_id" jdbcType="VARCHAR" property="lawEnforcId" />
    <result column="supervision_certificate_id" jdbcType="VARCHAR" property="supervisionCertificateId" />
    <result column="law_certificate_enabled" jdbcType="VARCHAR" property="lawCertificateEnabled" />
  </resultMap>
  <resultMap id="ItemDepartmentRelation" type="org.changneng.framework.frameworkbusiness.entity.ItemDepartmentRelation" >
    <id column="ID" property="id" jdbcType="VARCHAR" />
    <result column="ITEM_ID" property="itemId" jdbcType="VARCHAR" />
    <result column="DEPARTMENT_ID" property="departmentId" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 查询本部及本部一下所有部门使用 -->
   <resultMap id="BaseResultMapbm" type="org.changneng.framework.frameworkbusiness.entity.SysDepartment" >
    <id column="DEPARTMENT_ID" property="departmentId" jdbcType="VARCHAR" />
    <result column="DEPARTMENT_NUMBER" property="departmentNumber" jdbcType="VARCHAR" />
    <result column="DEPARTMENT_NAME" property="departmentName" jdbcType="VARCHAR" />
    <result column="PARENT_DEPTID" property="parentDeptid" jdbcType="VARCHAR" />
    <result column="PARENT_DEPTNAME" property="parentDeptname" jdbcType="VARCHAR" />
    <result column="BELONG_AREACODE" property="belongAreacode" jdbcType="VARCHAR" />
    <result column="BELONG_AREANAME" property="belongAreaname" jdbcType="VARCHAR" />
    <result column="IS_BELONG_TEAM" property="isBelongTeam" jdbcType="INTEGER" />
    <result column="ISLAWDEPT" property="islawdept" jdbcType="INTEGER" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="CREATE_USERID" property="createUserid" jdbcType="VARCHAR" />
    <result column="CREATE_USERNAME" property="createUsername" jdbcType="VARCHAR" />
    <result column="DEPARTMENT_STATE" property="departmentState" jdbcType="INTEGER" />
    <result column="LAST_UPDATE_DATE" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="UPDATE_USERID" property="updateUserid" jdbcType="VARCHAR" />
    <result column="UPDATE_USERNAME" property="updateUsername" jdbcType="VARCHAR" />
    <result column="IS_INIT" property="isInit" jdbcType="INTEGER" />
    <result column="MONITOR_COUNT" property="monitorCount" jdbcType="DECIMAL" />
    <result column="CASE_NUMBER_STATE" property="caseNumberState" jdbcType="INTEGER" />
    <result column="ISAPPLICATION" property="isApplication" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, LOGINNAME, PASSWORD, USERNAME, CARDID, PHONE, JOB_ID, JOB_NAME, ACCOUNT_NON_SYSTEM, 
    SYS_USER_NUMBER, LOGIN_NICKNAME, ORG_BELONG_DEPART_ID, ORG_BELONG_DEPART_NAME, ACCOUNT_NON_TEAM, 
    ACCOUNT_NON_LAWOFFICER,ACCOUNT_NON_STATION,LAW_DEVICE_ID, LAW_ENFORC_ID, LAW_EFFECTIVE_DATE, LAW_INVALID_DATE, 
    LAW_CERTIFICATE_ENABLED, SUPERVISION_CERTIFICATE_ID, SUPERVISION_EFFECTIVE_DATE, 
    SUPERVISION_INVALID_DATE, SUPERVISIO_CERTIFICATE_ENABLED, BELONG_DEPARTMENT_ID, BELONG_DEPARTMENT_NAME, 
    BELONG_AREA_ID, BELONG_AREA_NAME, DT_CREATE, LAST_LOGIN, DENDLINE, LOGIN_IP, ENABLED, 
    ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, LAST_UPDATE_DATE, 
    LAST_UPDATE_USER,DEPARTMENT_NUMBER,IS_LOGIN_TODAY,IS_SYSADMIN,IS_SNS,AVATAR_URL,FILE_ID,ACCOUNT_NON_POST,IS_DEFAULT_AVATAR,DEFAULT_AVATAR_URL,
    BIRTHDAY,GENDAR,EDUCATION,SILU_CREATE_DATE,IS_SILU,SILU_ID,SILU_AREA_CODE,
    START_LAW_ENFORCE_JOB_YEAR,GOOD_AT_LAW_ENFORCE_INDUSTRY,LAW_ENFORCE_CREDENTIAL,LAW_ENFORCE_CREDENTIAL_ID
  </sql>
   <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SYS_USERS
    where ID = #{id,jdbcType=VARCHAR}
  </select>

  <select id="selectGmByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from SYS_USERS_GM
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  
  <!-- 根据一个区划list查询出所属的用户ids -->
  <select id="selectUserIdsByAreaCodes" resultType="java.lang.String">
    select id from sys_users
    where ENABLED=1 and account_non_locked=1
	   and belong_area_id in
	<foreach collection="areaCodeList" index="index" item="item"  open="(" separator="," close=")">    
        #{item}
    </foreach> 
  </select>
  
  <select id="queryUserDeptName" resultType="java.lang.String">
  	select b.department_name
	  from sys_users a
	  left join sys_department b
	    on b.department_number = substr(a.department_number, 0, 9)
	 where a.id = #{id, jdbcType=VARCHAR}
  </select>
  
  <select id="getByLoginname" parameterType="java.lang.String"  resultMap="BaseResultMap">
  	select
  		<include refid="Base_Column_List" />
  		from sys_users
  		where username=#{loginname,jdbcType=VARCHAR} and enabled=1
  </select>
  
  <select id="getByUsername" parameterType="java.lang.String"  resultMap="BaseResultMap">
  	select
  		<include refid="Base_Column_List" />
  		from sys_users
  		where username=#{loginname,jdbcType=VARCHAR} and enabled !=0
  </select>
  
  <select id="checkUsernameForUpdate" parameterType="java.lang.String"  resultMap="BaseResultMap">
  		select
  		<include refid="Base_Column_List" />
  		from sys_users
  		where username=#{loginname,jdbcType=VARCHAR} and id!=#{updateid,jdbcType=VARCHAR} and enabled !=0
  </select>
  
  <select id="querySystemUsers" resultMap="BaseResultMap">
  	 	select su.*,sd.department_name as departmentName from sys_users su ,sys_department sd where su.belong_department_id=sd.department_id
  	 	<if test="search.belongAreaId!= null and search.belongAreaId != ''" >
        	and su.belong_area_id like CONCAT(#{search.belongAreaId},'%')
	    </if>
	    <if test="search.loginname != null and search.loginname != ''" >
	       	and (su.loginname like CONCAT('%',CONCAT(#{search.loginname},'%'))
	       	 or su.username like CONCAT('%',CONCAT(#{search.loginname},'%')))
	    </if>
	    and enabled=1
      <if test="search.isAdmin != null and search.isAdmin == '0'.toString()" >
        and is_sysadmin!=1
      </if>
	    order by su.belong_area_id,su.username
  </select>
  
  <select id="getUpdateUserByYesterday" resultMap="BaseResultMap">
  	 	SELECT * FROM ( SELECT TMP_PAGE.*, ROWNUM ROW_ID FROM
  	 		(select <include refid="Base_Column_List" /> 
		  	 from sys_users where enabled=1 and is_sysadmin!=1 and  ACCOUNT_NON_LOCKED = 1  
			 <if test="yesterday !=null">
			 	and to_char(LAST_UPDATE_DATE,'yyyy-mm-dd')&gt;=to_char(#{yesterday,jdbcType=TIMESTAMP},'yyyy-mm-dd')
			 </if>
			 order by ID
	    	) 
	    TMP_PAGE WHERE ROWNUM   &lt;=    #{pageSize} ) WHERE ROW_ID >  #{currPage} order by LAST_UPDATE_DATE desc,ID
  </select>
  <select id="getUpdateUserByYesterdayTotal"  resultType="int">
  	 	select count(1)
  	 	from sys_users where  enabled=1 and is_sysadmin!=1 and  ACCOUNT_NON_LOCKED = 1  
	    <if test="yesterday !=null">
	    	and to_char(LAST_UPDATE_DATE,'yyyy-mm-dd')&gt;=to_char(#{yesterday,jdbcType=TIMESTAMP},'yyyy-mm-dd')
	    </if>
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from SYS_USERS
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.SysUsers">
    insert into SYS_USERS (ID, LOGINNAME, PASSWORD, 
      USERNAME, CARDID, PHONE, 
      JOB_ID, JOB_NAME, ACCOUNT_NON_SYSTEM, 
      SYS_USER_NUMBER, LOGIN_NICKNAME, ORG_BELONG_DEPART_ID, 
      ORG_BELONG_DEPART_NAME, ACCOUNT_NON_TEAM, ACCOUNT_NON_LAWOFFICER,ACCOUNT_NON_STATION, 
      LAW_DEVICE_ID, LAW_ENFORC_ID, LAW_EFFECTIVE_DATE, 
      LAW_INVALID_DATE, LAW_CERTIFICATE_ENABLED, 
      SUPERVISION_CERTIFICATE_ID, SUPERVISION_EFFECTIVE_DATE, 
      SUPERVISION_INVALID_DATE, SUPERVISIO_CERTIFICATE_ENABLED, 
      BELONG_DEPARTMENT_ID, BELONG_DEPARTMENT_NAME, 
      BELONG_AREA_ID, BELONG_AREA_NAME, DT_CREATE, 
      LAST_LOGIN, DENDLINE, LOGIN_IP, 
      ENABLED, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, 
      CREDENTIALS_NON_EXPIRED, LAST_UPDATE_DATE, 
      LAST_UPDATE_USER,DEPARTMENT_NUMBER,IS_LOGIN_TODAY,IS_SYSADMIN,
      ACCOUNT_NON_POST,IS_DEFAULT_AVATAR,DEFAULT_AVATAR_URL,EDUCATION,BIRTHDAY,GENDAR,START_LAW_ENFORCE_JOB_YEAR,GOOD_AT_LAW_ENFORCE_INDUSTRY,LAW_ENFORCE_CREDENTIAL,LAW_ENFORCE_CREDENTIAL_ID)
    values (#{id,jdbcType=VARCHAR}, #{loginname,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, 
      #{username,jdbcType=VARCHAR}, #{cardid,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{jobId,jdbcType=VARCHAR}, #{jobName,jdbcType=VARCHAR}, #{accountNonSystem,jdbcType=DECIMAL}, 
      #{sysUserNumber,jdbcType=VARCHAR}, #{loginNickname,jdbcType=VARCHAR}, #{orgBelongDepartId,jdbcType=VARCHAR}, 
      #{orgBelongDepartName,jdbcType=VARCHAR}, #{accountNonTeam,jdbcType=DECIMAL}, #{accountNonLawofficer,jdbcType=DECIMAL},#{accountNonStation,jdbcType=DECIMAL}, 
      #{lawDeviceId,jdbcType=VARCHAR}, #{lawEnforcId,jdbcType=VARCHAR}, #{lawEffectiveDate,jdbcType=TIMESTAMP}, 
      #{lawInvalidDate,jdbcType=TIMESTAMP}, #{lawCertificateEnabled,jdbcType=DECIMAL}, 
      #{supervisionCertificateId,jdbcType=VARCHAR}, #{supervisionEffectiveDate,jdbcType=TIMESTAMP}, 
      #{supervisionInvalidDate,jdbcType=TIMESTAMP}, #{supervisioCertificateEnabled,jdbcType=DECIMAL}, 
      #{belongDepartmentId,jdbcType=VARCHAR}, #{belongDepartmentName,jdbcType=VARCHAR}, 
      #{belongAreaId,jdbcType=VARCHAR}, #{belongAreaName,jdbcType=VARCHAR}, #{dtCreate,jdbcType=TIMESTAMP}, 
      #{lastLogin,jdbcType=TIMESTAMP}, #{dendline,jdbcType=TIMESTAMP}, #{loginIp,jdbcType=VARCHAR}, 
      #{userEnabled,jdbcType=DECIMAL}, #{userNonExpired,jdbcType=DECIMAL}, #{userNonLocked,jdbcType=DECIMAL}, 
      #{credentialsUserNonExpired,jdbcType=DECIMAL}, #{lastUpdateDate,jdbcType=TIMESTAMP}, 
      #{lastUpdateUser,jdbcType=VARCHAR},#{departmentNumber,jdbcType=VARCHAR},#{isLoginToday,jdbcType=VARCHAR},
      #{isSysadmin,jdbcType=DECIMAL},#{accountNonPost,jdbcType=DECIMAL},#{isDefaultAvatar,jdbcType=DECIMAL},#{defaultAvatarUrl,jdbcType=VARCHAR},
      #{education,jdbcType=VARCHAR},#{birthday,jdbcType=TIMESTAMP},#{gendar,jdbcType=DECIMAL},#{startLawEnforceJobYear,jdbcType=VARCHAR},
      #{goodAtLawEnforceIndustry,jdbcType=VARCHAR},#{lawEnforceCredential,jdbcType=VARCHAR},#{lawEnforceCredentialId,jdbcType=VARCHAR}
      )
      
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.SysUsers">
    <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into SYS_USERS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="loginname != null">
        LOGINNAME,
      </if>
      <if test="password != null">
        PASSWORD,
      </if>
      <if test="username != null">
        USERNAME,
      </if>
      <if test="cardid != null">
        CARDID,
      </if>
      <if test="phone != null">
        PHONE,
      </if>
      <if test="jobId != null">
        JOB_ID,
      </if>
      <if test="jobName != null">
        JOB_NAME,
      </if>
      <if test="accountNonSystem != null">
        ACCOUNT_NON_SYSTEM,
      </if>
      <if test="sysUserNumber != null">
        SYS_USER_NUMBER,
      </if>
      <if test="loginNickname != null">
        LOGIN_NICKNAME,
      </if>
      <if test="orgBelongDepartId != null">
        ORG_BELONG_DEPART_ID,
      </if>
      <if test="orgBelongDepartName != null">
        ORG_BELONG_DEPART_NAME,
      </if>
      <if test="accountNonTeam != null">
        ACCOUNT_NON_TEAM,
      </if>
      <if test="accountNonLawofficer != null">
        ACCOUNT_NON_LAWOFFICER,
      </if>
      <if test="accountNonStation != null">
        ACCOUNT_NON_STATION,
      </if>
      <if test="lawDeviceId != null">
        LAW_DEVICE_ID,
      </if>
      <if test="lawEnforcId != null">
        LAW_ENFORC_ID,
      </if>
      <if test="lawEffectiveDate != null">
        LAW_EFFECTIVE_DATE,
      </if>
      <if test="lawInvalidDate != null">
        LAW_INVALID_DATE,
      </if>
      <if test="lawCertificateEnabled != null">
        LAW_CERTIFICATE_ENABLED,
      </if>
      <if test="supervisionCertificateId != null">
        SUPERVISION_CERTIFICATE_ID,
      </if>
      <if test="supervisionEffectiveDate != null">
        SUPERVISION_EFFECTIVE_DATE,
      </if>
      <if test="supervisionInvalidDate != null">
        SUPERVISION_INVALID_DATE,
      </if>
      <if test="supervisioCertificateEnabled != null">
        SUPERVISIO_CERTIFICATE_ENABLED,
      </if>
      <if test="belongDepartmentId != null">
        BELONG_DEPARTMENT_ID,
      </if>
      <if test="belongDepartmentName != null">
        BELONG_DEPARTMENT_NAME,
      </if>
      <if test="belongAreaId != null">
        BELONG_AREA_ID,
      </if>
      <if test="belongAreaName != null">
        BELONG_AREA_NAME,
      </if>
      <if test="dtCreate != null">
        DT_CREATE,
      </if>
      <if test="lastLogin != null">
        LAST_LOGIN,
      </if>
      <if test="dendline != null">
        DENDLINE,
      </if>
      <if test="loginIp != null">
        LOGIN_IP,
      </if>
      <if test="userEnabled != null">
        ENABLED,
      </if>
      <if test="userNonExpired != null">
        ACCOUNT_NON_EXPIRED,
      </if>
      <if test="userNonLocked != null">
        ACCOUNT_NON_LOCKED,
      </if>
      <if test="credentialsUserNonExpired != null">
        CREDENTIALS_NON_EXPIRED,
      </if>
      <if test="lastUpdateDate != null">
        LAST_UPDATE_DATE,
      </if>
      <if test="lastUpdateUser != null">
        LAST_UPDATE_USER,
      </if>
      <if test="departmentNumber != null">
        DEPARTMENT_NUMBER,
      </if>
      <if test="isLoginToday != null">
        IS_LOGIN_TODAY,
      </if>
      <if test="isSysadmin != null">
        IS_SYSADMIN,
      </if>
      <if test="accountNonPost != null">
        ACCOUNT_NON_POST,
      </if>
         <if test="isDefaultAvatar != null">
        IS_DEFAULT_AVATAR,
      </if>
         <if test="defaultAvatarUrl != null">
        DEFAULT_AVATAR_URL,
      </if>
      
         <if test="gendar != null">
        GENDAR,
      </if>
         <if test="birthday != null">
        BIRTHDAY,
      </if>
         <if test="education != null">
        EDUCATION,
      </if>
         <if test="isSilu != null">
        IS_SILU,
      </if>
         <if test="siluCreateDate != null">
        SILU_CREATE_DATE,
      </if>
         <if test="siluId != null">
        SILU_ID,
      </if>
         <if test="siluAreaCode != null">
        SILU_AREA_CODE,
      </if>
      <if test="startLawEnforceJobYear != null">
        START_LAW_ENFORCE_JOB_YEAR,
      </if>
      <if test="goodAtLawEnforceIndustry != null">
        GOOD_AT_LAW_ENFORCE_INDUSTRY,
      </if>
      <if test="lawEnforceCredential != null">
        LAW_ENFORCE_CREDENTIAL,
      </if>
      <if test="lawEnforceCredentialId != null">
        LAW_ENFORCE_CREDENTIAL_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="loginname != null">
        #{loginname,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="cardid != null">
        #{cardid,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="jobId != null">
        #{jobId,jdbcType=VARCHAR},
      </if>
      <if test="jobName != null">
        #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="accountNonSystem != null">
        #{accountNonSystem,jdbcType=DECIMAL},
      </if>
      <if test="sysUserNumber != null">
        #{sysUserNumber,jdbcType=VARCHAR},
      </if>
      <if test="loginNickname != null">
        #{loginNickname,jdbcType=VARCHAR},
      </if>
      <if test="orgBelongDepartId != null">
        #{orgBelongDepartId,jdbcType=VARCHAR},
      </if>
      <if test="orgBelongDepartName != null">
        #{orgBelongDepartName,jdbcType=VARCHAR},
      </if>
      <if test="accountNonTeam != null">
        #{accountNonTeam,jdbcType=DECIMAL},
      </if>
      <if test="accountNonLawofficer != null">
        #{accountNonLawofficer,jdbcType=DECIMAL},
      </if>
      <if test="accountNonStation != null">
        #{accountNonStation,jdbcType=DECIMAL},
      </if>
      <if test="lawDeviceId != null">
        #{lawDeviceId,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforcId != null">
        #{lawEnforcId,jdbcType=VARCHAR},
      </if>
      <if test="lawEffectiveDate != null">
        #{lawEffectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lawInvalidDate != null">
        #{lawInvalidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lawCertificateEnabled != null">
        #{lawCertificateEnabled,jdbcType=DECIMAL},
      </if>
      <if test="supervisionCertificateId != null">
        #{supervisionCertificateId,jdbcType=VARCHAR},
      </if>
      <if test="supervisionEffectiveDate != null">
        #{supervisionEffectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="supervisionInvalidDate != null">
        #{supervisionInvalidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="supervisioCertificateEnabled != null">
        #{supervisioCertificateEnabled,jdbcType=DECIMAL},
      </if>
      <if test="belongDepartmentId != null">
        #{belongDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="belongDepartmentName != null">
        #{belongDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaId != null">
        #{belongAreaId,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaName != null">
        #{belongAreaName,jdbcType=VARCHAR},
      </if>
      <if test="dtCreate != null">
        #{dtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastLogin != null">
        #{lastLogin,jdbcType=TIMESTAMP},
      </if>
      <if test="dendline != null">
        #{dendline,jdbcType=TIMESTAMP},
      </if>
      <if test="loginIp != null">
        #{loginIp,jdbcType=VARCHAR},
      </if>
      <if test="userEnabled != null">
        #{enabled,jdbcType=DECIMAL},
      </if>
      <if test="userNonExpired != null">
        #{accountNonExpired,jdbcType=DECIMAL},
      </if>
      <if test="userNonLocked != null">
        #{accountNonLocked,jdbcType=DECIMAL},
      </if>
      <if test="credentialsUserNonExpired != null">
        #{credentialsNonExpired,jdbcType=DECIMAL},
      </if>
      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUser != null">
        #{lastUpdateUser,jdbcType=VARCHAR},
      </if>
      <if test="departmentNumber != null">
        #{departmentNumber,jdbcType=VARCHAR},
      </if>
      <if test="isLoginToday != null">
        #{isLoginToday,jdbcType=VARCHAR},
      </if>
      <if test="isSysadmin != null">
        #{isSysadmin,jdbcType=DECIMAL},
      </if>
      <!-- 2017-07-05 动态插入 新增一条字段  开始-->
      <if test="accountNonPost != null">
         #{accountNonPost,jdbcType=DECIMAL},
      </if>
         <if test="isDefaultAvatar != null">
         #{isDefaultAvatar,jdbcType=DECIMAL},
      </if>
        <if test="defaultAvatarUrl != null">
        #{defaultAvatarUrl,jdbcType=VARCHAR},
      </if>
      <!-- 2017-07-05 动态插入 新增一条字段  结束-->
      <!-- 接着弄着 -->
        <if test="gendar != null">
         #{gendar,jdbcType=DECIMAL},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=TIMESTAMP},
      </if>
        <if test="education != null">
        #{education,jdbcType=VARCHAR},
        </if>
        <if test="isSilu != null">
         #{isSilu,jdbcType=DECIMAL},
      </if>
      <if test="siluCreateDate != null">
        #{siluCreateDate,jdbcType=TIMESTAMP},
      </if>
        <if test="siluId != null">
        #{siluId,jdbcType=VARCHAR},
      </if>
        <if test="siluAreaCode != null">
        #{siluAreaCode,jdbcType=VARCHAR},
      </if>
       <if test="startLawEnforceJobYear != null">
        #{startLawEnforceJobYear,jdbcType=VARCHAR},
      </if>
      <if test="goodAtLawEnforceIndustry != null">
        #{goodAtLawEnforceIndustry,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforceCredential != null">
        #{lawEnforceCredential,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforceCredentialId != null">
        #{lawEnforceCredentialId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <update id="updatePersonalInfo" parameterType="org.changneng.framework.frameworkbusiness.entity.SysUsers">
  	update SYS_USERS
    <set>
      <if test="phone != null">
        PHONE = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="loginNickname != null">
        LOGIN_NICKNAME = #{loginNickname,jdbcType=VARCHAR},
      </if>
      <if test="avatarUrl != null">
        AVATAR_URL = #{avatarUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=VARCHAR},
      </if>
       <if test="isDefaultAvatar != null">
        IS_DEFAULT_AVATAR = #{isDefaultAvatar},
      </if>
       <if test="defaultAvatarUrl != null">
        DEFAULT_AVATAR_URL = #{defaultAvatarUrl,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforceCredential != null">
        LAW_ENFORCE_CREDENTIAL = #{lawEnforceCredential,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforceCredentialId != null">
        LAW_ENFORCE_CREDENTIAL_ID = #{lawEnforceCredentialId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  
  </update>
  
  <update id="resetPassword" parameterType="org.changneng.framework.frameworkbusiness.entity.SysUsers">
  		update SYS_USERS
    <set>
      <if test="password != null">
        PASSWORD = #{password,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.SysUsers">
    update SYS_USERS
    <set>
      <if test="loginname != null">
        LOGINNAME = #{loginname,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password!=''">
        PASSWORD = #{password,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        USERNAME = #{username,jdbcType=VARCHAR},
      </if>
      <if test="cardid != null">
        CARDID = #{cardid,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        PHONE = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="jobId != null">
        JOB_ID = #{jobId,jdbcType=VARCHAR},
      </if>
      <if test="jobName != null">
        JOB_NAME = #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="accountNonSystem != null">
        ACCOUNT_NON_SYSTEM = #{accountNonSystem,jdbcType=DECIMAL},
      </if>
      <if test="sysUserNumber != null">
        SYS_USER_NUMBER = #{sysUserNumber,jdbcType=VARCHAR},
      </if>
      <if test="loginNickname != null">
        LOGIN_NICKNAME = #{loginNickname,jdbcType=VARCHAR},
      </if>
      <if test="orgBelongDepartId != null">
        ORG_BELONG_DEPART_ID = #{orgBelongDepartId,jdbcType=VARCHAR},
      </if>
      <if test="orgBelongDepartName != null">
        ORG_BELONG_DEPART_NAME = #{orgBelongDepartName,jdbcType=VARCHAR},
      </if>
      <if test="accountNonTeam != null">
        ACCOUNT_NON_TEAM = #{accountNonTeam,jdbcType=DECIMAL},
      </if>
      <if test="accountNonLawofficer != null">
        ACCOUNT_NON_LAWOFFICER = #{accountNonLawofficer,jdbcType=DECIMAL},
      </if>
      <if test="accountNonStation != null">
        ACCOUNT_NON_STATION = #{accountNonStation,jdbcType=DECIMAL},
      </if>
      <!-- 2017-07-05 动态插入 新增一条字段  开始-->
      <if test="accountNonPost != null">
        ACCOUNT_NON_POST = #{accountNonPost,jdbcType=DECIMAL},
      </if>
      <!-- 2017-07-05 动态插入 新增一条字段  结束-->
      <if test="lawDeviceId != null">
        LAW_DEVICE_ID = #{lawDeviceId,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforcId != null">
        LAW_ENFORC_ID = #{lawEnforcId,jdbcType=VARCHAR},
      </if>
      <if test="lawEffectiveDate != null">
        LAW_EFFECTIVE_DATE = #{lawEffectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lawInvalidDate != null">
        LAW_INVALID_DATE = #{lawInvalidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lawCertificateEnabled != null">
        LAW_CERTIFICATE_ENABLED = #{lawCertificateEnabled,jdbcType=DECIMAL},
      </if>
      <if test="supervisionCertificateId != null">
        SUPERVISION_CERTIFICATE_ID = #{supervisionCertificateId,jdbcType=VARCHAR},
      </if>
      <if test="supervisionEffectiveDate != null">
        SUPERVISION_EFFECTIVE_DATE = #{supervisionEffectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="supervisionInvalidDate != null">
        SUPERVISION_INVALID_DATE = #{supervisionInvalidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="supervisioCertificateEnabled != null">
        SUPERVISIO_CERTIFICATE_ENABLED = #{supervisioCertificateEnabled,jdbcType=DECIMAL},
      </if>
      <if test="belongDepartmentId != null">
        BELONG_DEPARTMENT_ID = #{belongDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="belongDepartmentName != null">
        BELONG_DEPARTMENT_NAME = #{belongDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaId != null">
        BELONG_AREA_ID = #{belongAreaId,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaName != null">
        BELONG_AREA_NAME = #{belongAreaName,jdbcType=VARCHAR},
      </if>
      <if test="dtCreate != null">
        DT_CREATE = #{dtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastLogin != null">
        LAST_LOGIN = #{lastLogin,jdbcType=TIMESTAMP},
      </if>
      <if test="dendline != null">
        DENDLINE = #{dendline,jdbcType=TIMESTAMP},
      </if>
      <if test="loginIp != null">
        LOGIN_IP = #{loginIp,jdbcType=VARCHAR},
      </if>
      <if test="userEnabled != null">
        ENABLED = #{enabled,jdbcType=DECIMAL},
      </if>
      <if test="userNonExpired != null">
        ACCOUNT_NON_EXPIRED = #{accountNonExpired,jdbcType=DECIMAL},
      </if>
      <if test="userNonLocked != null">
        ACCOUNT_NON_LOCKED = #{accountNonLocked,jdbcType=DECIMAL},
      </if>
      <if test="credentialsUserNonExpired != null">
        CREDENTIALS_NON_EXPIRED = #{credentialsNonExpired,jdbcType=DECIMAL},
      </if>
      <if test="lastUpdateDate != null">
        LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateUser != null">
        LAST_UPDATE_USER = #{lastUpdateUser,jdbcType=VARCHAR},
      </if>
      <if test="departmentNumber != null">
        DEPARTMENT_NUMBER = #{departmentNumber,jdbcType=VARCHAR},
      </if>
      <if test="isLoginToday != null">
        IS_LOGIN_TODAY = #{isLoginToday,jdbcType=VARCHAR},
      </if>
      <if test="isSysadmin != null">
      	IS_SYSADMIN = #{isSysadmin,jdbcType=DECIMAL},
      </if>
       <if test="avatarUrl != null">
      	AVATAR_URL = #{avatarUrl,jdbcType=VARCHAR},
      </if>
       <if test="fileId != null">
      	FILE_ID = #{fileId,jdbcType=VARCHAR},
      </if>
       <if test="isDefaultAvatar != null">
      	IS_DEFAULT_AVATAR = #{isDefaultAvatar,jdbcType=DECIMAL},
      </if>
         <if test="defaultAvatarUrl != null">
      	DEFAULT_AVATAR_URL = #{defaultAvatarUrl,jdbcType=VARCHAR},
      </if>
       <if test="isSns != null">
      	IS_SNS = #{isSns,jdbcType=DECIMAL},
      </if>
      <!-- 2018-07-18新增 开始 -->
      <if test="gendar != null">
      	GENDAR = #{gendar,jdbcType=DECIMAL},
      </if>
      <if test="birthday != null">
        BIRTHDAY = #{birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="education != null">
      	EDUCATION = #{education,jdbcType=VARCHAR},
      </if>
      <if test="isSilu != null">
      	IS_SILU = #{isSilu,jdbcType=DECIMAL},
      </if>
      <if test="siluCreateDate != null">
        SILU_CREATE_DATE = #{siluCreateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="siluId != null">
      	SILU_ID = #{siluId,jdbcType=VARCHAR},
      </if>
      <if test="siluAreaCode != null">
      	SILU_AREA_CODE = #{siluAreaCode,jdbcType=VARCHAR},
      </if>
      <!-- 2018-07-18新增 结束 -->
      <if test="startLawEnforceJobYear != null">
      	START_LAW_ENFORCE_JOB_YEAR = #{startLawEnforceJobYear,jdbcType=VARCHAR},
      </if>
      <if test="goodAtLawEnforceIndustry != null">
      	GOOD_AT_LAW_ENFORCE_INDUSTRY = #{goodAtLawEnforceIndustry,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforceCredential != null">
      	LAW_ENFORCE_CREDENTIAL = #{lawEnforceCredential,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforceCredentialId != null">
      	LAW_ENFORCE_CREDENTIAL_ID = #{lawEnforceCredentialId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
  <update id="updateByPrimaryKeySelectiveDateAndIp" parameterType="org.changneng.framework.frameworkbusiness.entity.SysUsers">
    update SYS_USERS
    <set>
      <if test="lastLogin != null">
        LAST_LOGIN = #{lastLogin,jdbcType=TIMESTAMP},
      </if>
      <if test="loginIp != null">
        LOGIN_IP = #{loginIp,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
  <update id="enabledAndDisabledUser" parameterType="java.lang.String">
  		update SYS_USERS
      	<if test="type==0">
      		 set account_non_locked = 1,LAST_UPDATE_DATE = sysdate 
	    </if>
       	<if test="type==1">
      		 set account_non_locked = 0,LAST_UPDATE_DATE = sysdate 
	    </if>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
  <update id="updateEnableById" parameterType="java.lang.String">
  	update SYS_USERS
      		 set enabled =0
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
  <update id="updateSnsStateById" parameterType="java.lang.String">
  	update SYS_USERS
      		 set is_sns=1
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.SysUsers">
    update SYS_USERS
    set LOGINNAME = #{loginname,jdbcType=VARCHAR},
      PASSWORD = #{password,jdbcType=VARCHAR},
      USERNAME = #{username,jdbcType=VARCHAR},
      CARDID = #{cardid,jdbcType=VARCHAR},
      PHONE = #{phone,jdbcType=VARCHAR},
      JOB_ID = #{jobId,jdbcType=VARCHAR},
      JOB_NAME = #{jobName,jdbcType=VARCHAR},
      ACCOUNT_NON_SYSTEM = #{accountNonSystem,jdbcType=DECIMAL},
      SYS_USER_NUMBER = #{sysUserNumber,jdbcType=VARCHAR},
      LOGIN_NICKNAME = #{loginNickname,jdbcType=VARCHAR},
      ORG_BELONG_DEPART_ID = #{orgBelongDepartId,jdbcType=VARCHAR},
      ORG_BELONG_DEPART_NAME = #{orgBelongDepartName,jdbcType=VARCHAR},
      ACCOUNT_NON_TEAM = #{accountNonTeam,jdbcType=DECIMAL},
      ACCOUNT_NON_LAWOFFICER = #{accountNonLawofficer,jdbcType=DECIMAL},
      ACCOUNT_NON_STATION = #{accountNonStation,jdbcType=DECIMAL},
      <!-- 2017-07-05 动态插入 新增一条字段  开始-->
      ACCOUNT_NON_POST = #{accountNonPost,jdbcType=DECIMAL},
      <!-- 2017-07-05 动态插入 新增一条字段  结束-->
      LAW_DEVICE_ID = #{lawDeviceId,jdbcType=VARCHAR},
      LAW_ENFORC_ID = #{lawEnforcId,jdbcType=VARCHAR},
      LAW_EFFECTIVE_DATE = #{lawEffectiveDate,jdbcType=TIMESTAMP},
      LAW_INVALID_DATE = #{lawInvalidDate,jdbcType=TIMESTAMP},
      LAW_CERTIFICATE_ENABLED = #{lawCertificateEnabled,jdbcType=DECIMAL},
      SUPERVISION_CERTIFICATE_ID = #{supervisionCertificateId,jdbcType=VARCHAR},
      SUPERVISION_EFFECTIVE_DATE = #{supervisionEffectiveDate,jdbcType=TIMESTAMP},
      SUPERVISION_INVALID_DATE = #{supervisionInvalidDate,jdbcType=TIMESTAMP},
      SUPERVISIO_CERTIFICATE_ENABLED = #{supervisioCertificateEnabled,jdbcType=DECIMAL},
      BELONG_DEPARTMENT_ID = #{belongDepartmentId,jdbcType=VARCHAR},
      BELONG_DEPARTMENT_NAME = #{belongDepartmentName,jdbcType=VARCHAR},
      BELONG_AREA_ID = #{belongAreaId,jdbcType=VARCHAR},
      BELONG_AREA_NAME = #{belongAreaName,jdbcType=VARCHAR},
      DT_CREATE = #{dtCreate,jdbcType=TIMESTAMP},
      LAST_LOGIN = #{lastLogin,jdbcType=TIMESTAMP},
      DENDLINE = #{dendline,jdbcType=TIMESTAMP},
      LOGIN_IP = #{loginIp,jdbcType=VARCHAR},
      ENABLED = #{userEnabled,jdbcType=DECIMAL},
      ACCOUNT_NON_EXPIRED = #{userNonExpired,jdbcType=DECIMAL},
      ACCOUNT_NON_LOCKED = #{userNonLocked,jdbcType=DECIMAL},
      CREDENTIALS_NON_EXPIRED = #{credentialsUserNonExpired,jdbcType=DECIMAL},
      LAST_UPDATE_DATE = #{lastUpdateDate,jdbcType=TIMESTAMP},
      LAST_UPDATE_USER = #{lastUpdateUser,jdbcType=VARCHAR},
      DEPARTMENT_NUMBER = #{departmentNumber,jdbcType=VARCHAR},
      IS_LOGIN_TODAY = #{isLoginToday,jdbcType=VARCHAR},
      IS_SYSADMIN=#{isSysadmin,jdbcType=DECIMAL},
      IS_DEFAULT_AVATAR=#{isDefaultAvatar,jdbcType=DECIMAL},
      DEFAULT_AVATAR_URL = #{defaultAvatarUrl,jdbcType=VARCHAR},
      GENDAR = #{gendar,jdbcType=DECIMAL},
      BIRTHDAY = #{birthday,jdbcType=TIMESTAMP},
      EDUCATION = #{education,jdbcType=VARCHAR},
      IS_SILU = #{isSilu,jdbcType=DECIMAL},
      SILU_CREATE_DATE = #{siluCreateDate,jdbcType=TIMESTAMP},
      SILU_ID = #{siluId,jdbcType=VARCHAR},
      SILU_AREA_CODE = #{siluAreaCode,jdbcType=VARCHAR},
      START_LAW_ENFORCE_JOB_YEAR = #{startLawEnforceJobYear,jdbcType=VARCHAR},
      GOOD_AT_LAW_ENFORCE_INDUSTRY = #{goodAtLawEnforceIndustry,jdbcType=VARCHAR},
      LAW_ENFORCE_CREDENTIAL = #{lawEnforceCredential,jdbcType=VARCHAR},
      LAW_ENFORCE_CREDENTIAL_ID = #{lawEnforceCredentialId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
   <!--CheckUserChooseList   CheckUserChooseBean-->
    
    <resultMap id="CheckUserChooseBean" type="org.changneng.framework.frameworkbusiness.entity.CheckUserChooseBean">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="USERNAME" jdbcType="VARCHAR" property="username" />
    <result column="LOGINNAME" jdbcType="VARCHAR" property="loginname" />
    <result column="JOB_ID" jdbcType="VARCHAR" property="jobId" />
    <result column="JOB_NAME" jdbcType="VARCHAR" property="jobName" />
    <result column="CARDID" jdbcType="VARCHAR" property="cardid" />
    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="LAW_ENFORC_ID" jdbcType="VARCHAR" property="lawEnforcId" />
    <result column="BELONG_AREA_ID" jdbcType="VARCHAR" property="belongAreaId" />
    <result column="BELONG_AREA_NAME" jdbcType="VARCHAR" property="belongAreaName" />
    <result column="SUPERVISION_CERTIFICATE_ID" jdbcType="VARCHAR" property="supervisionCertificateId" />
    <result column="BELONG_DEPARTMENT_ID" jdbcType="VARCHAR" property="belongDepartmentId" />
    <result column="BELONG_DEPARTMENT_NAME" jdbcType="VARCHAR" property="belongDepartmentName" />
    <result column="SUPERVISION_CERTIFICATE_ID" jdbcType="VARCHAR" property="supervisionCertificateId" />
    <result column="SUPERVISIO_CERTIFICATE_ENABLED" jdbcType="DECIMAL" property="supervisioCertificateEnabled" />
    <result column="LAW_CERTIFICATE_ENABLED" jdbcType="DECIMAL" property="lawCertificateEnabled" />
    <result column="LAW_ENFORC_ID" jdbcType="VARCHAR" property="lawEnforcId" />
  </resultMap>
  <!--  CheckUserChooseList  选择检查人默认加载当前人行政区划下 -->
<!--   <select id="CheckUserChooseList" parameterType="java.lang.String"  resultMap="CheckUserChooseBean">
  	select  *
  		from SYS_USERS s 
  		where 
      	 ACCOUNT_NON_LAWOFFICER = 1  and  (LAW_CERTIFICATE_ENABLED = 1 or  SUPERVISIO_CERTIFICATE_ENABLED = 1) and  ACCOUNT_NON_LOCKED  = 1 and ENABLED =1
         <if test ="sysUserId !='' and sysUserId != null">
         	xian 查询本级
        	 and s.ID != #{sysUserId}
         </if>
         <if test ="status == 1">
         省 查询所有
           order by s.ID   asc  
         </if>
        <if test ="status == 2">
         市  查询本级和下级
         
           and BELONG_AREA_ID like  '${citySubCode}%'
           order by s.ID asc 
         
         </if>
         <if test ="status == 3">
         县 查询本级
           and s.BELONG_AREA_ID = #{areaCode} 
           order by s.ID  asc 
         </if>
  </select> -->
  <!-- CheckUserChooseList -->
   <select id="CheckUserChooseList" parameterType="java.lang.String"  resultMap="CheckUserChooseBean">
  	 SELECT * FROM ( SELECT TMP_PAGE.*, ROWNUM ROW_ID FROM
 		( select * from SYS_USERS s where
		 ENABLED = 1 
 		 and (LAW_CERTIFICATE_ENABLED = 1 or SUPERVISIO_CERTIFICATE_ENABLED = 1) 
		and (LAW_ENFORC_ID is not null or SUPERVISION_CERTIFICATE_ID is not null)
		and ACCOUNT_NON_LOCKED = 1 
		  <if test ="sysUserId !='' and sysUserId != null">
        	 and s.ID != #{sysUserId}
         </if>
         <if test ="levelstatus == 1">
        	 order by  id 
         </if>
        <if test ="levelstatus == 2">
           and BELONG_AREA_ID like  '${citySubCode}%'
           order by  id 
         </if>
         <if test ="levelstatus == 3">
           and s.BELONG_AREA_ID = #{areaCode} 
           order by  id 
         </if>
			 ) TMP_PAGE WHERE ROWNUM   &lt;=    #{pageSize} ) WHERE ROW_ID >  #{currPage} order by belong_area_id asc 
  </select> 
  <!-- CheckUserTotal  查询总记录数-->
	  <select id="CheckUserTotal" parameterType="java.lang.String" resultType="int" >
	  select  count(1)
	  		from SYS_USERS s 
	  		where 
	      	  ENABLED = 1 
	 		 and (LAW_CERTIFICATE_ENABLED = 1 or SUPERVISIO_CERTIFICATE_ENABLED = 1) 
			and (LAW_ENFORC_ID is not null or SUPERVISION_CERTIFICATE_ID is not null)
			and ACCOUNT_NON_LOCKED = 1 
	          <if test ="sysUserId !='' and sysUserId != null">
	        	 and s.ID != #{sysUserId}
	         </if>
	         <if test ="levelstatus == 1">
	         </if>
	        <if test ="levelstatus == 2">
	           and BELONG_AREA_ID like  '${citySubCode}%'
	         </if>
	         <if test ="levelstatus == 3">
	           and s.BELONG_AREA_ID = #{areaCode} 
	         </if>
	  </select>
  
  <!-- getCaseChickUserList -->
   <select id="getCaseChickUserList" parameterType="java.lang.String"  resultMap="CheckUserChooseBean">
  	 SELECT * FROM ( SELECT TMP_PAGE.*, ROWNUM ROW_ID FROM
 		( select * from SYS_USERS s where
		 ENABLED = 1 
		 and ACCOUNT_NON_LOCKED = 1 
		  <if test ="sysUserId !='' and sysUserId != null">
        	 and s.ID != #{sysUserId}
         </if>
         <if test ="levelstatus == 1">
        	 order by  id 
         </if>
        <if test ="levelstatus == 2">
           and BELONG_AREA_ID like  '${citySubCode}%'
           order by  id 
         </if>
         <if test ="levelstatus == 3">
           and s.BELONG_AREA_ID = #{areaCode} 
           order by  id 
         </if>
			 ) TMP_PAGE WHERE ROWNUM   &lt;=    #{pageSize} ) WHERE ROW_ID >  #{currPage} order by belong_area_id asc 
  </select> 
  <!-- getCaseChickUserCount  查询总记录数-->
  <select id="getCaseChickUserCount" parameterType="java.lang.String" resultType="int" >
  select  count(1)
  		from SYS_USERS s 
  		where 
      	  ENABLED = 1 
		  and ACCOUNT_NON_LOCKED = 1 
          <if test ="sysUserId !='' and sysUserId != null">
        	 and s.ID != #{sysUserId}
         </if>
         <if test ="levelstatus == 1">
         </if>
        <if test ="levelstatus == 2">
           and BELONG_AREA_ID like  '${citySubCode}%'
         </if>
         <if test ="levelstatus == 3">
           and s.BELONG_AREA_ID = #{areaCode} 
         </if>
  </select>
      <select id="CheckUserChooseAllList" parameterType="java.lang.String"  resultMap="CheckUserChooseBean">
    	 SELECT * FROM ( SELECT TMP_PAGE.*, ROWNUM ROW_ID FROM
 		( select * from SYS_USERS s where
 		 ENABLED = 1 
 		 and (LAW_CERTIFICATE_ENABLED = 1 or SUPERVISIO_CERTIFICATE_ENABLED = 1) 
		and (LAW_ENFORC_ID is not null or SUPERVISION_CERTIFICATE_ID is not null)
		and ACCOUNT_NON_LOCKED = 1 
  		 <if test ="userName != '' and userName != null ">
         <!-- 县 查询本级-->
           and ( LOGINNAME   like '%${userName}%' or userName  like '%${userName}%')
         </if>
          <if test ="sysUserId !='' and sysUserId != null">
        	 and s.ID != #{sysUserId}
         </if>
         <if test ="levelstatus == 1">
         <!-- 省级 -->
           order by s.ID   asc  
         </if>
         <if test ="levelstatus == 2 ">
         <!-- 市查询本级-->
           and s.BELONG_AREA_ID like  '${citySubCode}%'
           order by s.ID   asc  
         </if>
           <if test ="levelstatus == 3">
         	<!-- xian 查询本级-->
        	 and s.BELONG_AREA_ID = #{areaCode}
             order by s.ID  asc 
         </if>
          ) TMP_PAGE WHERE ROWNUM   &lt;=    #{pageSize} ) WHERE ROW_ID >  #{currPage} order by belong_area_id asc 
  </select>

	<select id="getCheckUserChooseAllList"  resultMap="CheckUserChooseBean">
		select *
		from SYS_USERS sus
		<where>
			sus.ENABLED = 1 
			and (sus.LAW_CERTIFICATE_ENABLED = 1 or sus.SUPERVISIO_CERTIFICATE_ENABLED = 1)
			and (sus.LAW_ENFORC_ID is not null or sus.SUPERVISION_CERTIFICATE_ID is not null) 
			and sus.ACCOUNT_NON_LOCKED = 1
			<if test="searchBean.userName != '' and searchBean.userName != null ">
				and sus.LOGINNAME like CONCAT(CONCAT('%',#{searchBean.userName}),'%')  
			</if>
			<!-- 省 无需判断，全部拿出 -->
			<!-- 市  -->
			<if test="searchBean.level == 2 ">
				and sus.BELONG_AREA_ID like  CONCAT(#{searchBean.belongAreaCode},'%') 
			</if>
			<!-- 县 -->
			<if test="searchBean.level == 3">
				and sus.BELONG_AREA_ID = #{searchBean.belongAreaCode}
			</if>
		</where>
		order by sus.belong_area_id asc ,sus.ID
	</select>
  
  <!-- CheckUserAllTotal -->
     <select id="CheckUserAllTotal" parameterType="java.lang.String"  resultType="int">
    select  count(1)
  		from SYS_USERS s 
  		where  
      	  ENABLED = 1 
 		 and (LAW_CERTIFICATE_ENABLED = 1 or SUPERVISIO_CERTIFICATE_ENABLED = 1) 
		and (LAW_ENFORC_ID is not null or SUPERVISION_CERTIFICATE_ID is not null)
		and ACCOUNT_NON_LOCKED = 1 
  		 <if test ="userName != '' and userName != null ">
         <!-- 县 查询本级-->
           and LOGINNAME   like '%${userName}%'
         </if>
         <if test ="sysUserId !='' and sysUserId != null">
        	 and s.ID != #{sysUserId}
         </if>
         <if test ="levelstatus == 1">
         <!-- 省级 -->
         </if>
         <if test ="levelstatus == 2 ">
         <!-- 市查询本级-->
           and s.BELONG_AREA_ID like  '${citySubCode}%'
         </if>
           <if test ="levelstatus == 3">
         	<!-- xian 查询本级-->
        	 and s.BELONG_AREA_ID = #{areaCode}
         </if>
  </select>
  <!--getCaseChickUserAllList  -->
       <select id="getCaseChickUserAllList" parameterType="java.lang.String"  resultMap="CheckUserChooseBean">
    	 SELECT * FROM ( SELECT TMP_PAGE.*, ROWNUM ROW_ID FROM
 		( select * from SYS_USERS s where
 		 ENABLED = 1 
		 and ACCOUNT_NON_LOCKED = 1 
  		 <if test ="userName != '' and userName != null ">
         <!-- 县 查询本级-->
           and LOGINNAME   like '%${userName}%'
         </if>
          <if test ="sysUserId !='' and sysUserId != null">
        	 and s.ID != #{sysUserId}
         </if>
         <if test ="levelstatus == 1">
         <!-- 省级 -->
           order by s.ID   asc  
         </if>
         <if test ="levelstatus == 2 ">
         <!-- 市查询本级-->
           and s.BELONG_AREA_ID like  '${citySubCode}%'
           order by s.ID   asc  
         </if>
           <if test ="levelstatus == 3">
         	<!-- xian 查询本级-->
        	 and s.BELONG_AREA_ID = #{areaCode}
             order by s.ID  asc 
         </if>
          ) TMP_PAGE WHERE ROWNUM   &lt;=    #{pageSize} ) WHERE ROW_ID >  #{currPage} order by belong_area_id asc 
  </select>
  <!-- getCaseChickUserAllCount -->
     <select id="getCaseChickUserAllCount" parameterType="java.lang.String"  resultType="int">
    select  count(1)
  		from SYS_USERS s 
  		where  
      	  ENABLED = 1 
		  and ACCOUNT_NON_LOCKED = 1 
  		 <if test ="userName != '' and userName != null ">
         <!-- 县 查询本级-->
           and LOGINNAME   like '%${userName}%'
         </if>
         <if test ="sysUserId !='' and sysUserId != null">
        	 and s.ID != #{sysUserId}
         </if>
         <if test ="levelstatus == 1">
         <!-- 省级 -->
         </if>
         <if test ="levelstatus == 2 ">
         <!-- 市查询本级-->
           and s.BELONG_AREA_ID like  '${citySubCode}%'
         </if>
           <if test ="levelstatus == 3">
         	<!-- xian 查询本级-->
        	 and s.BELONG_AREA_ID = #{areaCode}
         </if>
  </select>
  <!--  getAreaList-->
      <select id="getAreaList" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    DISTINCT BELONG_AREA_ID
    from SYS_USERS
    where ID in (${checUserIds})
  </select>
  <update id="resetLoginState">
  	update sys_users
  	set is_login_today='0'
  </update>
  
   <select id="getUserByDepartmentNumber" parameterType="java.lang.String" resultMap="AppSysUsers">
   	select id,LOGINNAME,law_enforc_id,supervision_certificate_id,law_certificate_enabled 
    from SYS_USERS
    where department_number  like CONCAT(#{departmentNumber},'%')
      and ENABLED = 1 
      and (LAW_CERTIFICATE_ENABLED = 1 or SUPERVISIO_CERTIFICATE_ENABLED = 1) 
      and (LAW_ENFORC_ID is not null or SUPERVISION_CERTIFICATE_ID is not null)
	  and ACCOUNT_NON_LOCKED = 1 
	  <if test ="sysUserId !='' and sysUserId != null">
        	 and ID != #{sysUserId}
      </if>
    order BY ID
  </select>
  <select id="getSysUserByInformantId" parameterType="java.lang.String" resultMap="BaseResultMap">
	  	SELECT
		ID,--登录人id
		LOGINNAME,--登录人姓名
		BELONG_AREA_ID,--区划code
		BELONG_AREA_NAME,--区划name
		BELONG_DEPARTMENT_ID,--部门id
		BELONG_DEPARTMENT_NAME,--部门name
		PHONE--联系人id
	FROM
		SYS_USERS 
	where 
		ID=#{sd}
  </select>
  <select id="getSysUserAll" parameterType="java.lang.String"  resultMap="BaseResultMap">
  	select
  		<include refid="Base_Column_List" />
  		from sys_users where enabled !=0
  </select>
  <select id="getMaxLastUpdateTimeForSilu" parameterType="java.lang.String"  resultType="java.util.Date">
  	select max(LAST_UPDATE_DATE) from SYS_USERS where enabled !=0 and  is_silu='1'
  </select>
  
   <select id="selectDepartName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    SU.ORG_BELONG_DEPART_ID,
	SU.ORG_BELONG_DEPART_NAME,
	SU.BELONG_DEPARTMENT_ID,
	SU.BELONG_DEPARTMENT_NAME
    FROM
	sys_users SU where SU.ID = #{creatUserId,jdbcType=VARCHAR}
  </select>
  <!-- 根据事项id拿到部门id去取本部门下的人员 -->
<!--   <select id="getUserList" parameterType="java.lang.String"  resultMap="BaseResultMap"> -->
<!--   	select -->
<!--   		us.* -->
<!--   		from sys_users  us, ITEM_DEPARTMENT_RELATION     it -->
<!--   		where us.BELONG_DEPARTMENT_ID = it.DEPARTMENT_ID -->
<!--   		<if test ="itemId !='' and itemId != null"> -->
<!--         	 and it.ITEM_ID=#{itemId,jdbcType=VARCHAR}  -->
<!--       </if> -->
<!--        and ENABLED = 1  -->
<!--        and (LAW_CERTIFICATE_ENABLED = 1 or SUPERVISIO_CERTIFICATE_ENABLED = 1)  -->
<!--   	   and (LAW_ENFORC_ID is not null or SUPERVISION_CERTIFICATE_ID is not null) -->
<!--   	   and ACCOUNT_NON_LAWOFFICER = 1 -->
<!--   </select> -->
  <!-- 根据事项id拿到部门id去取本部门下的人员 -->
  <select id="getUserList" parameterType="java.lang.String"  resultMap="BaseResultMap">
  	select
  		us.*
  		from sys_users  us
  		where us.BELONG_DEPARTMENT_ID in 
			(
				SELECT DEPARTMENT_ID FROM sys_department 
				START WITH department_id IN
			(
				select DEPARTMENT_ID from ITEM_DEPARTMENT_RELATION 
				where 
        	  		   ITEM_ID = #{itemId,jdbcType=VARCHAR} 
     			
			)
				CONNECT BY PRIOR department_id=parent_deptid

			)
       and ACCOUNT_NON_LOCKED = 1	 
       and ENABLED = 1 
       and (LAW_CERTIFICATE_ENABLED = 1 or SUPERVISIO_CERTIFICATE_ENABLED = 1) 
  	   and (LAW_ENFORC_ID is not null or SUPERVISION_CERTIFICATE_ID is not null)
  	   and ACCOUNT_NON_LAWOFFICER = 1
  </select>
  <select id="selectBySiluUser"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SYS_USERS
    where IS_SILU=1
  </select>
  <!-- 2019-6-12 一园一档   队伍信息中执法人员来自执法系统(根据所属行政区查询执法人员) -->
   <select id="getLawPerson"  parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT 
     	 <include refid="Base_Column_List" />
    FROM SYS_USERS
    WHERE ENABLED = 1
		AND ACCOUNT_NON_LOCKED = 1
		AND ACCOUNT_NON_TEAM = 1
		AND BELONG_AREA_ID LIKE CONCAT(#{code},'%')
  </select>

  <select id="getUserByPhone" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT * from SYS_USERS where 1=1 and account_non_locked=1 and ENABLED =1
    <if test="phone != null and phone!=''">
      and phone = #{phone}
    </if>
  </select>
</mapper>