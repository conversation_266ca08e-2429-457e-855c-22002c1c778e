package org.changneng.framework.frameworkbusiness.entity.filecase;

import java.util.Date;
/**
 * 公共的一键生成条目信息类
 * <AUTHOR>
 * 2018年2月24日
 */
public class OneKeyContent {

	private String id;

	private String name;

	private String forceCatalogName;

	private String objectId;

	private Integer typeCode;

	private String typeName;

	private String itemId;

	private Date createDate;

	private Integer location;

	private Integer isDel;

	private String itemRemark;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name == null ? null : name.trim();
	}

	public String getForceCatalogName() {
		return forceCatalogName;
	}

	public void setForceCatalogName(String forceCatalogName) {
		this.forceCatalogName = forceCatalogName == null ? null : forceCatalogName.trim();
	}

	public String getObjectId() {
		return objectId;
	}

	public void setObjectId(String objectId) {
		this.objectId = objectId == null ? null : objectId.trim();
	}

	public Integer getTypeCode() {
		return typeCode;
	}

	public void setTypeCode(Integer typeCode) {
		this.typeCode = typeCode;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName == null ? null : typeName.trim();
	}

	public String getItemId() {
		return itemId;
	}

	public void setItemId(String itemId) {
		this.itemId = itemId == null ? null : itemId.trim();
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getLocation() {
		return location;
	}

	public void setLocation(Integer location) {
		this.location = location;
	}

	public Integer getIsDel() {
		return isDel;
	}

	public void setIsDel(Integer isDel) {
		this.isDel = isDel;
	}

	public String getItemRemark() {
		return itemRemark;
	}

	public void setItemRemark(String itemRemark) {
		this.itemRemark = itemRemark;
	}
}
