package org.changneng.framework.frameworkbusiness.service.filecase;

import java.util.List;
import java.util.Set;

import org.changneng.framework.frameworkbusiness.entity.JsonResult;
import org.changneng.framework.frameworkbusiness.entity.butt.SpotPerson;

public interface SendApplyForce {
	
	/**
	 * 申请法院强制执行发送数据
	 * @param accessToken 用户
	 * @param errorList 基本信息发送错误的数据
	 */
	void  sendApplyForceFunction(String accessToken,Set<String> errorList,boolean status);
	
	/**
	 * 按日计罚 发送数据接口
	 * @param accessToken
	 * @param errorList
	 */
	void sendPenaltyDayFunction(String accessToken,Set<String> errorList,boolean status);
	
	/**
	 * 环境污染犯罪 发送接口
	 * @param accessToken
	 * @param errorList
	 */
	void sendPollutionCrimeFunction(String accessToken,Set<String> errorList,boolean status);
	
	/**
	 * 发送对接数据信息
	 * @param postUrl 路径
	 * @param dataMap 数据集合
	 * @param accessToken 密钥
	 * @param caseId 基本信息ID
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 * @date 2018年6月11日-下午2:05:13
	 */
	JsonResult postSendFront(String postUrl,Object dataMap ,String accessToken,String caseId) throws Exception;
	
	/**
	 * 在对接过程中，主要执法人员信息集合加工公共方法。<br>
	 * 该方法把我们系统中CaseLawEnforcement集合转换为需要对接的SpotPerson集合。
     * @param modelerType 1：基本信息，2：简易行政处罚，3：一般行政处罚，4：行政命令，5：查封扣押，6：限产停产，7：行政拘留，
						  8：环境污染犯罪，9：申请法院强制执行，10：其他移送，11：按日计罚
     * @param infoId
	 * @return
	 * <AUTHOR>
	 * @date 2018年6月11日-下午2:08:05
	 */
	List<SpotPerson> processingSpotPerson(String modelerType,String infoId); 
}
