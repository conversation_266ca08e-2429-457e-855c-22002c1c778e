package org.changneng.framework.frameworkbusiness.entity;

import java.util.Date;

import org.hibernate.validator.constraints.NotBlank;

public class SysRoles {
    private String roleId;

    private String roleName;
    
    @NotBlank(message="请填写角色名称")
    private String roleRealName;

	private String roleNum;

    private String roleDesc;

    private Integer roleIsEnabled;

    private String roleAreacode;

    private Date roleCreateDate;

    private String roleCreateUserid;

    private String roleCreateUsername;

    private Date roleLastUpdateDate;

    private String roleUpdateUsername;

    private String roleUpdateUserid;
    
    private String departName;
    
    private String suId;

    

	public String getSuId() {
		return suId;
	}

	public void setSuId(String suId) {
		this.suId = suId;
	}

	public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId == null ? null : roleId.trim();
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName == null ? null : roleName.trim();
    }
    

    public String getRoleRealName() {
		return roleRealName;
	}

	public void setRoleRealName(String roleRealName) {
		this.roleRealName = roleRealName == null ? null : roleRealName.trim();
	}

	public String getRoleNum() {
		return roleNum;
	}

	public void setRoleNum(String roleNum) {
		this.roleNum = roleNum;
	}    

    public String getRoleDesc() {
        return roleDesc;
    }

    public void setRoleDesc(String roleDesc) {
        this.roleDesc = roleDesc == null ? null : roleDesc.trim();
    }

    public Integer getRoleIsEnabled() {
        return roleIsEnabled;
    }

    public void setRoleIsEnabled(Integer roleIsEnabled) {
        this.roleIsEnabled = roleIsEnabled;
    }

    public String getRoleAreacode() {
        return roleAreacode;
    }

    public void setRoleAreacode(String roleAreacode) {
        this.roleAreacode = roleAreacode == null ? null : roleAreacode.trim();
    }

    public Date getRoleCreateDate() {
        return roleCreateDate;
    }

    public void setRoleCreateDate(Date roleCreateDate) {
        this.roleCreateDate = roleCreateDate;
    }

    public String getRoleCreateUserid() {
        return roleCreateUserid;
    }

    public void setRoleCreateUserid(String roleCreateUserid) {
        this.roleCreateUserid = roleCreateUserid == null ? null : roleCreateUserid.trim();
    }

    public String getRoleCreateUsername() {
        return roleCreateUsername;
    }

    public void setRoleCreateUsername(String roleCreateUsername) {
        this.roleCreateUsername = roleCreateUsername == null ? null : roleCreateUsername.trim();
    }

    public Date getRoleLastUpdateDate() {
        return roleLastUpdateDate;
    }

    public void setRoleLastUpdateDate(Date roleLastUpdateDate) {
        this.roleLastUpdateDate = roleLastUpdateDate;
    }

    public String getRoleUpdateUsername() {
        return roleUpdateUsername;
    }

    public void setRoleUpdateUsername(String roleUpdateUsername) {
        this.roleUpdateUsername = roleUpdateUsername == null ? null : roleUpdateUsername.trim();
    }

    public String getRoleUpdateUserid() {
        return roleUpdateUserid;
    }

    public void setRoleUpdateUserid(String roleUpdateUserid) {
        this.roleUpdateUserid = roleUpdateUserid == null ? null : roleUpdateUserid.trim();
    }

	public String getDepartName() {
		return departName;
	}

	public void setDepartName(String departName) {
		this.departName = departName;
	}

}