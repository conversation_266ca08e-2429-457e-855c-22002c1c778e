package org.changneng.framework.frameworkbusiness.service.impl;

import java.util.List;

import org.changneng.framework.frameworkbusiness.dao.FeedbackFilesMapper;
import org.changneng.framework.frameworkbusiness.dao.SysFilesMapper;
import org.changneng.framework.frameworkbusiness.entity.FeedbackFiles;
import org.changneng.framework.frameworkbusiness.service.FeedbackFilesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
public class FeedbackServiceImpl implements FeedbackFilesService {

	@Autowired
	private FeedbackFilesMapper feedbackFilesMapper;
	
	@Autowired
	private SysFilesMapper sysFilesMapper;
	
	@Override
	public List<FeedbackFiles> queryFilesByFeedbackId(String taskId) {
		List<FeedbackFiles> filesList = feedbackFilesMapper.selectFilesByTaskId(taskId);
		return filesList;
	}

	@Override
	public FeedbackFiles selectByPrimaryKeyVal(String id) {
		return feedbackFilesMapper.selectByPrimaryKey(id);
	}

	@Override
	public void deletefileInfo(String id) {
		FeedbackFiles file = feedbackFilesMapper.selectByPrimaryKey(id);
		feedbackFilesMapper.deletefileInfo(id);
		//设置系统文件为脏数据
		sysFilesMapper.updateSysFileDidable(file.getFileId());
	}

}
