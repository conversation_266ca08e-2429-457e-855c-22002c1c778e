package org.changneng.framework.frameworkbusiness.service.security;

import java.io.IOException;

import javax.servlet.RequestDispatcher;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.access.AccessDeniedHandler;

/**  
* 
* @ClassName: RequestAccessDeniedHandler
* @Description: TODO
* <AUTHOR>
* @date 2017年3月18日 下午4:52:24
*
*/
public class RequestAccessDeniedHandler implements AccessDeniedHandler {
	
	private String errorPage;
	
	@Override
	public void handle(HttpServletRequest request, HttpServletResponse response,
			AccessDeniedException accessDeniedException) throws IOException, ServletException {
		if(isAjaxRequest(request)){
			response.setHeader("Content-Type", "application/json;charset=UTF-8");
            response.getWriter().write(JacksonUtils.toJsonString(new ResponseJson().failure("403","001","权限不足","ajax请求权限不足",null)));
            response.getWriter().close();
		}else{
			if (!response.isCommitted()) {
				if (errorPage != null) {
					// Put exception into request scope (perhaps of use to a view)
					request.setAttribute(WebAttributes.ACCESS_DENIED_403,
							accessDeniedException);

					// Set the 403 status code.
					response.setStatus(HttpServletResponse.SC_FORBIDDEN);

					// forward to error page.
					RequestDispatcher dispatcher = request.getRequestDispatcher(errorPage);
					dispatcher.forward(request, response);
				}
				else {
					response.sendError(HttpServletResponse.SC_FORBIDDEN,
							accessDeniedException.getMessage());
				}
			}
		}
	}
	
	private boolean isAjaxRequest(HttpServletRequest request) {
        String header = request.getHeader("X-Requested-With");
        if (header != null && "XMLHttpRequest".equals(header))
            return true;
        else
            return false;
    }
	
	
	/**
	 * The error page to use. Must begin with a "/" and is interpreted relative to the
	 * current context root.
	 *
	 * @param errorPage the dispatcher path to display
	 *
	 * @throws IllegalArgumentException if the argument doesn't comply with the above
	 * limitations
	 */
	public void setErrorPage(String errorPage) {
		if ((errorPage != null) && !errorPage.startsWith("/")) {
			throw new IllegalArgumentException("errorPage must begin with '/'");
		}

		this.errorPage = errorPage;
	}
}
