<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.OopenInfoMapper" >
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.OopenInfo" >
    <id column="ID" property="id" jdbcType="VARCHAR" />
    <result column="CHECK_SUBJECT" property="checkSubject" jdbcType="VARCHAR" />
    <result column="CHECK_CODE" property="checkCode" jdbcType="VARCHAR" />
    <result column="PUBLIC_TITLE" property="publicTitle" jdbcType="VARCHAR" />
    <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
    <result column="SUBMIT_DATE" property="submitDate" jdbcType="TIMESTAMP" />
    <result column="STATE" property="state" jdbcType="VARCHAR" />
    <result column="IS_DELETE" property="isDelete" jdbcType="VARCHAR" />
    <result column="PUBLIC_STATE" property="publicState" jdbcType="VARCHAR" />
    <result column="SUBMIT_ID" property="submitId" jdbcType="VARCHAR" />
    <result column="SUBMIT_NAME" property="submitName" jdbcType="VARCHAR" />
    <result column="STORAGE_DATE" property="storageDate" jdbcType="TIMESTAMP" />
    <result column="END_MOD_DATE" property="endModDate" jdbcType="TIMESTAMP" />
    <result column="PROPELL_DATE" property="propellDate" jdbcType="TIMESTAMP" />
    <result column="RETURN_MESS" property="returnMess" jdbcType="CLOB" />
  </resultMap>

  <resultMap id="RandomTaskOpenInfoSearch" type="org.changneng.framework.frameworkbusiness.entity.RandomTaskOpenInfoSearch">
    <result column="checkSubject" property="checkSubject" jdbcType="VARCHAR" />
    <result column="publicTitle" property="publicTitle" jdbcType="VARCHAR" />
    <result column="publicState" property="publicState" jdbcType="VARCHAR" />
    <result column="state" property="state" jdbcType="VARCHAR" />
    <result column="propellDate" property="propellDate" jdbcType="TIMESTAMP" />
    <result column="submitDate" property="submitDate" jdbcType="TIMESTAMP" />
    <result column="returnMess" property="returnMess" jdbcType="VARCHAR" />

  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="org.changneng.framework.frameworkbusiness.entity.OopenInfoWithBLOBs" extends="BaseResultMap" >
    <result column="MESSAGE" property="message" jdbcType="CLOB" />
    <result column="RETURN_MESS" property="returnMess" jdbcType="CLOB" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, CHECK_SUBJECT, PUBLIC_TITLE, FILE_NAME, SUBMIT_DATE, STATE, IS_DELETE, PUBLIC_STATE, 
    SUBMIT_ID, SUBMIT_NAME, STORAGE_DATE, END_MOD_DATE,PROPELL_DATE,CHECK_CODE
  </sql>
  <sql id="Blob_Column_List" >
    MESSAGE, RETURN_MESS
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from O_OPEN_INFO
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from O_OPEN_INFO
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.OopenInfoWithBLOBs" >
    insert into O_OPEN_INFO (ID, CHECK_SUBJECT, PUBLIC_TITLE, 
      FILE_NAME, SUBMIT_DATE, STATE, 
      IS_DELETE, PUBLIC_STATE, SUBMIT_ID, 
      SUBMIT_NAME, STORAGE_DATE, END_MOD_DATE, 
      MESSAGE, RETURN_MESS,PROPELL_DATE,CHECK_CODE)
    values (#{id,jdbcType=VARCHAR}, #{checkSubject,jdbcType=VARCHAR},  #{checkCode,jdbcType=VARCHAR}, #{publicTitle,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{submitDate,jdbcType=TIMESTAMP}, #{state,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=VARCHAR}, #{publicState,jdbcType=VARCHAR}, #{submitId,jdbcType=VARCHAR}, 
      #{submitName,jdbcType=VARCHAR}, #{storageDate,jdbcType=TIMESTAMP}, #{endModDate,jdbcType=TIMESTAMP}, 
      #{message,jdbcType=CLOB}, #{returnMess,jdbcType=CLOB},#{propellDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.OopenInfoWithBLOBs" >
  <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into O_OPEN_INFO
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="checkSubject != null" >
        CHECK_SUBJECT,
      </if>
       <if test="checkCode != null" >
        CHECK_CODE,
      </if>
      <if test="publicTitle != null" >
        PUBLIC_TITLE,
      </if>
      <if test="fileName != null" >
        FILE_NAME,
      </if>
      <if test="submitDate != null" >
        SUBMIT_DATE,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="publicState != null" >
        PUBLIC_STATE,
      </if>
      <if test="submitId != null" >
        SUBMIT_ID,
      </if>
      <if test="submitName != null" >
        SUBMIT_NAME,
      </if>
      <if test="storageDate != null" >
        STORAGE_DATE,
      </if>
      <if test="endModDate != null" >
        END_MOD_DATE,
      </if>
      <if test="message != null" >
        MESSAGE,
      </if>
      <if test="returnMess != null" >
        RETURN_MESS,
      </if>
      <if test="propellDate != null" >
        PROPELL_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="checkSubject != null" >
        #{checkSubject,jdbcType=VARCHAR},
      </if>
       <if test="checkCode != null" >
       #{checkCode,jdbcType=VARCHAR},
      </if>
      <if test="publicTitle != null" >
        #{publicTitle,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="submitDate != null" >
        #{submitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null" >
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=VARCHAR},
      </if>
      <if test="publicState != null" >
        #{publicState,jdbcType=VARCHAR},
      </if>
      <if test="submitId != null" >
        #{submitId,jdbcType=VARCHAR},
      </if>
      <if test="submitName != null" >
        #{submitName,jdbcType=VARCHAR},
      </if>
      <if test="storageDate != null" >
        #{storageDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endModDate != null" >
        #{endModDate,jdbcType=TIMESTAMP},
      </if>
      <if test="message != null" >
        #{message,jdbcType=CLOB},
      </if>
      <if test="returnMess != null" >
        #{returnMess,jdbcType=CLOB},
      </if>
       <if test="propellDate != null" >
        #{propellDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.OopenInfoWithBLOBs" >
    update O_OPEN_INFO
    <set >
      <if test="checkSubject != null" >
        CHECK_SUBJECT = #{checkSubject,jdbcType=VARCHAR},
      </if>
       <if test="checkCode != null" >
       CHECK_CODE =  #{checkCode,jdbcType=VARCHAR},
      </if>
      <if test="publicTitle != null" >
        PUBLIC_TITLE = #{publicTitle,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="submitDate != null" >
        SUBMIT_DATE = #{submitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null" >
        STATE = #{state,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=VARCHAR},
      </if>
      <if test="publicState != null" >
        PUBLIC_STATE = #{publicState,jdbcType=VARCHAR},
      </if>
      <if test="submitId != null" >
        SUBMIT_ID = #{submitId,jdbcType=VARCHAR},
      </if>
      <if test="submitName != null" >
        SUBMIT_NAME = #{submitName,jdbcType=VARCHAR},
      </if>
      <if test="storageDate != null" >
        STORAGE_DATE = #{storageDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endModDate != null" >
        END_MOD_DATE = #{endModDate,jdbcType=TIMESTAMP},
      </if>
      <if test="message != null" >
        MESSAGE = #{message,jdbcType=CLOB},
      </if>
      <if test="returnMess != null" >
        RETURN_MESS = #{returnMess,jdbcType=CLOB},
      </if>
       <if test="propellDate != null" >
        PROPELL_DATE = #{propellDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="org.changneng.framework.frameworkbusiness.entity.OopenInfoWithBLOBs" >
    update O_OPEN_INFO
    set CHECK_SUBJECT = #{checkSubject,jdbcType=VARCHAR},
      PUBLIC_TITLE = #{publicTitle,jdbcType=VARCHAR},
      FILE_NAME = #{fileName,jdbcType=VARCHAR},
      SUBMIT_DATE = #{submitDate,jdbcType=TIMESTAMP},
      STATE = #{state,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=VARCHAR},
      PUBLIC_STATE = #{publicState,jdbcType=VARCHAR},
      SUBMIT_ID = #{submitId,jdbcType=VARCHAR},
      SUBMIT_NAME = #{submitName,jdbcType=VARCHAR},
      STORAGE_DATE = #{storageDate,jdbcType=TIMESTAMP},
      END_MOD_DATE = #{endModDate,jdbcType=TIMESTAMP},
      MESSAGE = #{message,jdbcType=CLOB},
      RETURN_MESS = #{returnMess,jdbcType=CLOB},
      PROPELL_DATE = #{propellDate,jdbcType=TIMESTAMP},
      CHECK_CODE =  #{checkCode,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.OopenInfo" >
    update O_OPEN_INFO
    set CHECK_SUBJECT = #{checkSubject,jdbcType=VARCHAR},
      PUBLIC_TITLE = #{publicTitle,jdbcType=VARCHAR},
      FILE_NAME = #{fileName,jdbcType=VARCHAR},
      SUBMIT_DATE = #{submitDate,jdbcType=TIMESTAMP},
      STATE = #{state,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=VARCHAR},
      PUBLIC_STATE = #{publicState,jdbcType=VARCHAR},
      SUBMIT_ID = #{submitId,jdbcType=VARCHAR},
      SUBMIT_NAME = #{submitName,jdbcType=VARCHAR},
      STORAGE_DATE = #{storageDate,jdbcType=TIMESTAMP},
      END_MOD_DATE = #{endModDate,jdbcType=TIMESTAMP},
      PROPELL_DATE = #{propellDate,jdbcType=TIMESTAMP},
       CHECK_CODE =  #{checkCode,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
    
  </update>
  	<select id="selectOpenInfo"  resultMap="BaseResultMap">
   		select 
   		<include refid="Base_Column_List" /> 
   		from O_OPEN_INFO 
   	</select>
  <!-- 网站公开列表 -->
  <select id="getOpenInfoList"    resultMap="ResultMapWithBLOBs"  >
		select  info.id  , 
		info.check_subject ,
		info.public_title, 
		 info.submit_date , 
		 info.state,
		 info.public_state ,
	    info.is_delete, 
	     info.return_mess,
         info.propell_date 
		from O_OPEN_INFO  info
		<where>
				1=1 and info.is_delete='0' 
				<if test="openInfoSearch.options == 1" >
			       and info.state='1'
			        
					<if test="openInfoSearch.publicState!= null and openInfoSearch.publicState !='' ">
						and info.public_state  = #{openInfoSearch.publicState}   
					</if>
					
					<if test="openInfoSearch.propeDateBeg!= null and openInfoSearch.propeDateBeg !='' ">
						 	and info.propell_date&gt;= to_date(#{openInfoSearch.propeDateBeg},'yyyy-MM-dd')
					</if>
					<if test="openInfoSearch.propeDateEnd!= null and openInfoSearch.propeDateEnd !='' ">
						 	and info.propell_date&lt;= to_date(#{openInfoSearch.propeDateEnd},'yyyy-MM-dd')+1
					</if>
		       </if>
		       
		        <if test="openInfoSearch.options == 2" >
		             and info.state !='3'
			          <if test="openInfoSearch.state != null and openInfoSearch.state !='' ">
						and info.state  = #{openInfoSearch.state}   
					</if>
					
					<if test="openInfoSearch.propeDateBeg!= null and openInfoSearch.propeDateBeg !='' ">
						 	and info.submit_date&gt;= to_date(#{openInfoSearch.propeDateBeg},'yyyy-MM-dd')
					</if>
					<if test="openInfoSearch.propeDateEnd!= null and openInfoSearch.propeDateEnd !='' ">
						 	and info.submit_date&lt;= to_date(#{openInfoSearch.propeDateEnd},'yyyy-MM-dd')+1
					</if>
		        </if>
			<if test="openInfoSearch.publicTitle!= null and openInfoSearch.publicTitle !='' ">
				and info.public_title like  CONCAT(CONCAT('%', #{openInfoSearch.publicTitle}),'%')      
			</if>
			<if test="openInfoSearch.checkSubject!= null and openInfoSearch.checkSubject !='' ">
				and info.check_subject like  CONCAT(CONCAT('%',#{openInfoSearch.checkSubject}),'%')          
			</if>
			
		</where>
		order by 
		<if test="openInfoSearch.options == 2" >
	      info.state desc,info.storage_date desc,info.submit_date desc
        </if>
        
		<if test="openInfoSearch.options == 1" >
	          info.public_state ,info.storage_date desc,info.propell_date desc
        </if>
		
  </select>
  
 
  
  <update id="deleteById" parameterType="org.changneng.framework.frameworkbusiness.entity.OopenInfo" >
    update O_OPEN_INFO
      set 
        IS_DELETE = '1'
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
  <update id="propellById" parameterType="org.changneng.framework.frameworkbusiness.entity.OopenInfo" >
    update O_OPEN_INFO
      set 
        STATE = '3'
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
</mapper>