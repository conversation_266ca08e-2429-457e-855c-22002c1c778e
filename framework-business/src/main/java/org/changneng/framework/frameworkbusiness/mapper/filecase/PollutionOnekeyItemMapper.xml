<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.filecase.PollutionOnekeyItemMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.filecase.PollutionOnekeyItem">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="POLLUTION_CATALOG_NAME" jdbcType="VARCHAR" property="pollutionCatalogName" />
    <result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId" />
    <result column="ITEM_ID" jdbcType="VARCHAR" property="itemId" />
    <result column="TYPE_CODE" jdbcType="DECIMAL" property="typeCode" />
    <result column="TYPE_NAME" jdbcType="VARCHAR" property="typeName" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="LOCATION" jdbcType="DECIMAL" property="location" />
    <result column="IS_DEL" jdbcType="DECIMAL" property="isDel" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="org.changneng.framework.frameworkbusiness.entity.filecase.PollutionOnekeyItem">
    <result column="ITEM_REMARK" jdbcType="BLOB" property="itemRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, NAME, POLLUTION_CATALOG_NAME, OBJECT_ID, ITEM_ID, TYPE_CODE, TYPE_NAME, CREATE_DATE, 
    LOCATION, IS_DEL
  </sql>
  <sql id="Blob_Column_List">
    ITEM_REMARK
  </sql>
  <update id="deleteByObjId">
  	update POLLUTION_ONEKEY_ITEM
  	set is_del=1
  	where object_id=#{objectId, jdbcType=VARCHAR}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from POLLUTION_ONEKEY_ITEM
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectByObjId" resultType="String">
  	select id 
  	from POLLUTION_ONEKEY_ITEM
  	where object_id=#{objectId, jdbcType=VARCHAR}
  	AND IS_DEL=0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from POLLUTION_ONEKEY_ITEM
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.PollutionOnekeyItem">
    insert into POLLUTION_ONEKEY_ITEM (ID, NAME, POLLUTION_CATALOG_NAME, 
      OBJECT_ID, ITEM_ID, TYPE_CODE, TYPE_NAME, 
      CREATE_DATE, LOCATION, IS_DEL, 
      ITEM_REMARK)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{pollutionCatalogName,jdbcType=VARCHAR}, 
      #{objectId,jdbcType=VARCHAR}, #{itemId,jdbcType=VARCHAR}, #{typeCode,jdbcType=DECIMAL}, #{typeName,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{location,jdbcType=DECIMAL}, #{isDel,jdbcType=DECIMAL}, 
      #{itemRemark,jdbcType=CLOB})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.PollutionOnekeyItem">
    insert into POLLUTION_ONEKEY_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
        ID,
      <if test="name != null">
        NAME,
      </if>
      <if test="pollutionCatalogName != null">
        POLLUTION_CATALOG_NAME,
      </if>
      <if test="objectId != null">
        OBJECT_ID,
      </if>
      <if test="itemId != null">
        ITEM_ID,
      </if>
      <if test="typeCode != null">
        TYPE_CODE,
      </if>
      <if test="typeName != null">
        TYPE_NAME,
      </if>
      <if test="createDate != null">
        CREATE_DATE,
      </if>
      <if test="location != null">
        LOCATION,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="itemRemark != null">
        ITEM_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
       sys_guid(),
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="pollutionCatalogName != null">
        #{pollutionCatalogName,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null">
        #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="typeCode != null">
        #{typeCode,jdbcType=DECIMAL},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="location != null">
        #{location,jdbcType=DECIMAL},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="itemRemark != null">
        #{itemRemark,jdbcType=CLOB},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.PollutionOnekeyItem">
    update POLLUTION_ONEKEY_ITEM
    <set>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="pollutionCatalogName != null">
        POLLUTION_CATALOG_NAME = #{pollutionCatalogName,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null">
        OBJECT_ID = #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null">
        ITEM_ID = #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="typeCode != null">
        TYPE_CODE = #{typeCode,jdbcType=DECIMAL},
      </if>
      <if test="typeName != null">
        TYPE_NAME = #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="location != null">
        LOCATION = #{location,jdbcType=DECIMAL},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=DECIMAL},
      </if>
      <if test="itemRemark != null">
        ITEM_REMARK = #{itemRemark,jdbcType=CLOB},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.PollutionOnekeyItem">
    update POLLUTION_ONEKEY_ITEM
    set NAME = #{name,jdbcType=VARCHAR},
      POLLUTION_CATALOG_NAME = #{pollutionCatalogName,jdbcType=VARCHAR},
      OBJECT_ID = #{objectId,jdbcType=VARCHAR},
      ITEM_ID = #{itemId,jdbcType=VARCHAR},
      TYPE_CODE = #{typeCode,jdbcType=DECIMAL},
      TYPE_NAME = #{typeName,jdbcType=VARCHAR},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      LOCATION = #{location,jdbcType=DECIMAL},
      IS_DEL = #{isDel,jdbcType=DECIMAL},
      ITEM_REMARK = #{itemRemark,jdbcType=CLOB}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.PollutionOnekeyItem">
    update POLLUTION_ONEKEY_ITEM
    set NAME = #{name,jdbcType=VARCHAR},
      POLLUTION_CATALOG_NAME = #{pollutionCatalogName,jdbcType=VARCHAR},
      OBJECT_ID = #{objectId,jdbcType=VARCHAR},
      ITEM_ID = #{itemId,jdbcType=VARCHAR},
      TYPE_CODE = #{typeCode,jdbcType=DECIMAL},
      TYPE_NAME = #{typeName,jdbcType=VARCHAR},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      LOCATION = #{location,jdbcType=DECIMAL},
      IS_DEL = #{isDel,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>