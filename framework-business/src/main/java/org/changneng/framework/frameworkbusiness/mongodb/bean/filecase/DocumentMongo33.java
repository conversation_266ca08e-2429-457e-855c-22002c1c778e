package org.changneng.framework.frameworkbusiness.mongodb.bean.filecase;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 文书制作-33-移送涉嫌环境违法适用行政拘留处罚案件审批表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017年11月22日 下午8:13:19
 */
@Document(collection = "ExecuDocMongo")
public class DocumentMongo33 {
	@Id
	private String id;

	/**
	 * 审批号第一部分
	 */
	private String approvalNum1;
	
	/**
	 * 审批号第二部分
	 */
	private String approvalNum2;
	/**
	 * 行政处罚
     * 决定书文号
	 */
	private String caseReason;
	
	/**
	 * 简要案情及
     * 查处经过
	 */
	private String briefCase;
	
	/**
	 * 企业名称或其他经营者
	 */
	private String lawObjectName;
	
	/**
	 * 组织机构代码
	 */
	private String orgCode;
	
	/**
	 * 地址
	 */
	private String address;
	
	/**
	 * 邮政编码
	 */
	private String postalNumber;
	
	/**
	 * 法人
	 */
	private String legalPerson;
	
	/**
	 * 法人身份证号
	 */
	private String legalManIdCard;
	
	/**
	 * 法人手机号
	 */
	private String legalPhone;
	
	/**
	 * 环保负责人
	 */
	private String chargePerson;
	
	/**
	 * 环保负责人身份证号
	 */
	private String chargeManIdCard;
	
	/**
	 * 环保负责人联系方式
	 */
	private String chargePersonPhone;
	
	/**
	 * 调查人员
	 */
	private String investigator;
	
	/**
	 * 承办部门
	 */
	private String departmentName;
	
	/**
	 * 行政拘留处罚移送依据和处理意见
	 */
	private String transferAdvice;
	
	/**
	 * 调查机构（厅局）
	 */
	private String userDept;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getApprovalNum1() {
		return approvalNum1;
	}

	public void setApprovalNum1(String approvalNum1) {
		this.approvalNum1 = approvalNum1;
	}

	public String getApprovalNum2() {
		return approvalNum2;
	}

	public void setApprovalNum2(String approvalNum2) {
		this.approvalNum2 = approvalNum2;
	}

	public String getCaseReason() {
		return caseReason;
	}

	public void setCaseReason(String caseReason) {
		this.caseReason = caseReason;
	}

	public String getBriefCase() {
		return briefCase;
	}

	public void setBriefCase(String briefCase) {
		this.briefCase = briefCase;
	}

	public String getInvestigator() {
		return investigator;
	}

	public void setInvestigator(String investigator) {
		this.investigator = investigator;
	}

	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	public String getTransferAdvice() {
		return transferAdvice;
	}

	public void setTransferAdvice(String transferAdvice) {
		this.transferAdvice = transferAdvice;
	}

	public String getUserDept() {
		return userDept;
	}

	public void setUserDept(String userDept) {
		this.userDept = userDept;
	}

	public String getLawObjectName() {
		return lawObjectName;
	}

	public void setLawObjectName(String lawObjectName) {
		this.lawObjectName = lawObjectName;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getPostalNumber() {
		return postalNumber;
	}

	public void setPostalNumber(String postalNumber) {
		this.postalNumber = postalNumber;
	}

	public String getLegalPerson() {
		return legalPerson;
	}

	public void setLegalPerson(String legalPerson) {
		this.legalPerson = legalPerson;
	}

	public String getLegalManIdCard() {
		return legalManIdCard;
	}

	public void setLegalManIdCard(String legalManIdCard) {
		this.legalManIdCard = legalManIdCard;
	}

	public String getLegalPhone() {
		return legalPhone;
	}

	public void setLegalPhone(String legalPhone) {
		this.legalPhone = legalPhone;
	}

	public String getChargePerson() {
		return chargePerson;
	}

	public void setChargePerson(String chargePerson) {
		this.chargePerson = chargePerson;
	}

	public String getChargeManIdCard() {
		return chargeManIdCard;
	}

	public void setChargeManIdCard(String chargeManIdCard) {
		this.chargeManIdCard = chargeManIdCard;
	}

	public String getChargePersonPhone() {
		return chargePersonPhone;
	}

	public void setChargePersonPhone(String chargePersonPhone) {
		this.chargePersonPhone = chargePersonPhone;
	}
	
}
