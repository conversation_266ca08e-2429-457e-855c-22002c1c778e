package org.changneng.framework.frameworkbusiness.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.changneng.framework.frameworkbusiness.entity.AdministrativeReviewSearch;
import org.changneng.framework.frameworkbusiness.entity.AdministrativeReviewWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.LocalCheck;
import org.changneng.framework.frameworkbusiness.entity.SysFiles;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.XzfyResultListBean;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.PageBean;

/**
 * 行政复议业务类
 * <AUTHOR>
 *
 */


public interface AdministrativeReviewService {
	/**
	 * 查询行政复议列表
	 * @param reBloBs
	 * @return
	 */
	public PageBean<AdministrativeReviewWithBLOBs> selectAdministrativeReviewList(AdministrativeReviewSearch search);
	/**
	 * 通过id查询行政复议信息
	 * @param id
	 * @return
	 */
	public AdministrativeReviewWithBLOBs selectAdministrativeInfo(String id);
	/**
	 * 上传附件  公共方法
	 * @param request
	 * @param response
	 * @param sysUsers
	 * @return
	 * @throws BusinessException
	 */
	public List<SysFiles> uploadPic(HttpServletRequest request, HttpServletResponse response, SysUsers sysUsers) throws BusinessException;
	/**
	 * 复习分析-获取统计数据
	 * @param searchListBean
	 * @return
	 */
	public XzfyResultListBean getResultFromStatistic();
	/**
	 * 新增/修改行政复议
	 * @param record
	 */
	public void addOrUpdateAdmin(AdministrativeReviewWithBLOBs record)throws Exception;
	/**
	 * 重复校验 input实时返回
	 * @param decisionNumber
	 * @param name
	 * @param id
	 * @return
	 */
	public List<AdministrativeReviewWithBLOBs> checkRepeat(String decisionNumber, String name,String id);
	/**
	 * 删除行政复议
	 * @param ids
	 */
	public void delAdministrativeReview(String ids);
	
	List<SysFiles> uploadPicByAdminReview(HttpServletRequest request, HttpServletResponse response, SysUsers sysUsers)
			throws BusinessException;
	public void deleteFileInfoById(String id,String recordId);
	/**
	 * 保存之前的重复校验
	 * @param decisionNumber
	 * @param name
	 * @param id
	 * @return
	 */
	public void saveCheckRepeat(String decisionNumber, String name,String id)throws Exception;
}
