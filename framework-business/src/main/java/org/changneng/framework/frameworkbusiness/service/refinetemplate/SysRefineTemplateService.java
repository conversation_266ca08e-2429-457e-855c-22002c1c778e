package org.changneng.framework.frameworkbusiness.service.refinetemplate;


import java.util.List;
import java.util.Map;

import org.changneng.framework.frameworkbusiness.entity.AskingCustomBean;
import org.changneng.framework.frameworkbusiness.entity.AskingCustomModel;
import org.changneng.framework.frameworkbusiness.entity.JsonResult;
import org.changneng.framework.frameworkbusiness.entity.LocalCheckItemBean;
import org.changneng.framework.frameworkbusiness.entity.SceneCustomModel;
import org.changneng.framework.frameworkbusiness.entity.SceneItemDatabase;
import org.changneng.framework.frameworkbusiness.entity.SceneSysModel;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.refinetemplate.RefineTemplateResult;
import org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneCheckEntry;
import org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneCustomModelSeach;
import org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneItemDatabaseSeach;
import org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneSysModelResult;
import org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneSysModelSeach;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

public interface SysRefineTemplateService {
	/**
	 * 查询系统模版信息
	 * @param sceneSysModelSeach 
	 * @return
	 */
	PageBean<SceneSysModelResult> selectSceneSysModelBySeachInfo(SceneSysModelSeach sceneSysModelSeach);
	/**
	 * 根据id删除系统模版表数据
	 * @param id
	 * @return
	 */
	JsonResult deleteSysRefineTemplateById(String id) throws Exception;
	/**
	 * 获取检查项列表
	 * @param pageNum
	 * @param pageSize
	 * @param sceneItemDatabaseSeach
	 * @return
	 */
	PageBean<SceneItemDatabase> getChickItemList(int pageNum, int pageSize,SceneItemDatabaseSeach sceneItemDatabaseSeach,SysUsers sysUser);
	/**
	 * 预览页面所需信息列表
	 * @param id  模版id
	 * @param modelType 模版类型：1.系统模版 2.自定义模版 0.根据检查项id查询
	 * @return
	 */
	List<RefineTemplateResult> selectRefineTemplateByIdAndType(String id,String modelType);
	/**
	 * 根据主键id和check_item_status 查询检查项信息(新字典表scene_check_entry)
	 * 获取到多选和下拉列表的选项内容
	 *  
	 * @param List<RefineTemplateResult> 存放检查项集合
	 * @param type 当为1时代表类型为多选框   当为3时代表类型为下拉框
	 * @return
	 */
	Map<String,List<SceneCheckEntry>> selectSceneCheckEntryInfoByIdAndType(List<RefineTemplateResult> list,String type);
	/**
	 * 根据检查项id删除检查项信息
	 * @param id
	 * @return
	 */
	JsonResult deleteChickItem(String id) throws Exception;;
	
	/**
	 * 通过条件查询
	 * @param sceneCustomModelSeach
	 * @return
	 */
	PageBean<SceneCustomModel> selectSceneCustomModelById(SceneCustomModelSeach sceneCustomModelSeach,SysUsers sysUsers);
	/**
	 * 新增模版(自定义模版转换为系统模板)
	 * @param id
	 * @return
	 */
	ResponseJson addRefineTemplateById(String id) throws Exception;
	
	/**
	 * 系统模版 修改默认模板
	 * @param sceneSysModelSeach
	 * @param operationType 操作类型
	 * @return
	 */
	JsonResult setDefault(SceneSysModelSeach sceneSysModelSeach,String operationType)throws Exception;
	/**
	 * 插入检查项信息
	 * @param SceneItemDatabase
	 * @return
	 */
	ResponseJson addCheckItem(SceneItemDatabase sceneItemDatabase) throws Exception;
	/**
	 * 系统模板- 保存为系统模板
	 * @param sceneSysModel
	 * @return
	 * @throws Exception 
	 */
	ResponseJson saveItemTemplate(SceneSysModel sceneSysModel) throws Exception;
	/**
	 * 根据系统的id查询检查项
	 * @param sysModelerId
	 * @return
	 */
	LocalCheckItemBean sysCheckItemList(String sysModelerId);
	/**
	 * 存为自定义模板
	 * @param sceneCustomModel
	 * @return
	 */
	ResponseJson saveCustorItemTemplate(SceneCustomModel sceneCustomModel,SysUsers sysUser);
	JsonResult setUsuallyOrDefaultOrDelete(AskingCustomBean customBean);
	/**
	 * 查询所有自定义模板
	 * @param sceneCustomModelSeach
	 * @param sysUser
	 * @return
	 */
	PageBean<SceneCustomModel> sceneCustomModelAllList(
			SceneCustomModelSeach sceneCustomModelSeach, SysUsers sysUser);
}
